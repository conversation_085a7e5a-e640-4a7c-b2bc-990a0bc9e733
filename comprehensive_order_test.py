#!/usr/bin/env python3
"""
全面的订单执行测试脚本
测试订单执行、手续费计算、持仓管理和盈亏计算
"""

import asyncio
import websockets
import json
import ssl
import requests
import time
import logging
from urllib3.exceptions import InsecureRequestWarning
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置
WS_URL = "wss://127.0.0.1:8082"
HTTP_BASE_URL = "https://127.0.0.1:8083"

@dataclass
class TestOrder:
    """测试订单"""
    symbol: str
    side: str  # BUY/SELL
    order_type: str  # MARKET/LIMIT
    quantity: str
    price: Optional[str] = None
    client_order_id: Optional[str] = None

@dataclass
class AccountSnapshot:
    """账户快照"""
    timestamp: str
    balance: float
    available_balance: float
    unrealized_pnl: float
    positions: Dict[str, Dict]

@dataclass
class OrderResult:
    """订单执行结果"""
    order_id: str
    client_order_id: str
    symbol: str
    side: str
    order_type: str
    quantity: float
    filled_quantity: float
    average_price: float
    commission: float
    status: str

class OrderTestFramework:
    """订单测试框架"""

    def __init__(self):
        self.websocket = None
        self.ssl_context = self._create_ssl_context()
        self.order_updates = []
        self.account_snapshots = []
        self.order_results = []
        self.current_prices = {}

    def _create_ssl_context(self):
        """创建SSL上下文"""
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        return ssl_context

    async def connect_websocket(self):
        """连接WebSocket"""
        try:
            self.websocket = await websockets.connect(WS_URL, ssl=self.ssl_context)
            logger.info("✅ WebSocket连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            return False

    async def subscribe_to_streams(self):
        """订阅数据流"""
        if not self.websocket:
            return False

        try:
            # 订阅订单更新
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["test_listen_key"],
                "id": 1
            }
            await self.websocket.send(json.dumps(subscribe_msg))
            response = await self.websocket.recv()
            logger.info(f"📥 订单更新订阅响应: {response}")

            # 订阅BBO数据
            bbo_subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["btcusdt@bookTicker"],
                "id": 2
            }
            await self.websocket.send(json.dumps(bbo_subscribe_msg))
            response = await self.websocket.recv()
            logger.info(f"📥 BBO订阅响应: {response}")

            return True
        except Exception as e:
            logger.error(f"❌ 订阅失败: {e}")
            return False

    def get_account_info(self) -> Optional[Dict]:
        """获取账户信息"""
        try:
            response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/account", verify=False)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"❌ 获取账户信息失败: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"❌ 获取账户信息异常: {e}")
            return None

    def get_balance_info(self) -> Optional[List[Dict]]:
        """获取余额信息"""
        try:
            response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/balance", verify=False)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"❌ 获取余额信息失败: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"❌ 获取余额信息异常: {e}")
            return None

    def take_account_snapshot(self, label: str = "") -> AccountSnapshot:
        """拍摄账户快照"""
        account_info = self.get_account_info()
        balance_info = self.get_balance_info()

        if not account_info or not balance_info:
            logger.error("❌ 无法获取账户信息")
            return None

        # 提取关键信息
        balance = float(balance_info[0]['balance']) if balance_info else 0.0
        available_balance = float(account_info.get('availableBalance', 0))
        unrealized_pnl = float(account_info.get('totalUnrealizedProfit', 0))

        # 提取持仓信息
        positions = {}
        for pos in account_info.get('positions', []):
            if float(pos.get('positionAmt', 0)) != 0:
                positions[pos['symbol']] = {
                    'quantity': float(pos['positionAmt']),
                    'entry_price': float(pos['entryPrice']),
                    'unrealized_pnl': float(pos['unrealizedProfit']),
                    'margin': float(pos.get('isolatedMargin', 0))
                }

        snapshot = AccountSnapshot(
            timestamp=datetime.now().isoformat(),
            balance=balance,
            available_balance=available_balance,
            unrealized_pnl=unrealized_pnl,
            positions=positions
        )

        self.account_snapshots.append((label, snapshot))
        logger.info(f"📸 账户快照 [{label}]: 余额={balance:.6f}, 可用={available_balance:.6f}, 未实现盈亏={unrealized_pnl:.6f}")

        return snapshot

    async def place_order(self, order: TestOrder) -> bool:
        """下单"""
        if not self.websocket:
            logger.error("❌ WebSocket未连接")
            return False

        try:
            order_msg = {
                "id": int(time.time() * 1000),
                "method": "order.place",
                "params": {
                    "symbol": order.symbol,
                    "side": order.side,
                    "type": order.order_type,
                    "quantity": order.quantity,
                    "newClientOrderId": order.client_order_id or f"test_{int(time.time() * 1000)}"
                }
            }

            if order.price:
                order_msg["params"]["price"] = order.price

            logger.info(f"📤 发送订单: {json.dumps(order_msg, indent=2)}")
            await self.websocket.send(json.dumps(order_msg))

            # 等待订单确认
            response = await self.websocket.recv()
            logger.info(f"📥 订单响应: {response}")

            return True
        except Exception as e:
            logger.error(f"❌ 下单失败: {e}")
            return False

    async def listen_for_updates(self, duration: int = 5):
        """监听订单更新"""
        if not self.websocket:
            return

        logger.info(f"🎧 开始监听更新 ({duration}秒)...")
        start_time = time.time()

        try:
            while time.time() - start_time < duration:
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    data = json.loads(message)

                    # 处理不同类型的消息
                    if 'stream' in data:
                        stream_name = data['stream']
                        stream_data = data['data']

                        if 'bookTicker' in stream_name:
                            # BBO数据
                            self.current_prices[stream_data['s']] = {
                                'bid': float(stream_data['b']),
                                'ask': float(stream_data['a']),
                                'mid': (float(stream_data['b']) + float(stream_data['a'])) / 2
                            }
                            logger.info(f"📊 BBO更新 {stream_data['s']}: bid={stream_data['b']}, ask={stream_data['a']}")

                        elif stream_name == 'test_listen_key':
                            # 订单更新
                            if stream_data.get('e') == 'ORDER_TRADE_UPDATE':
                                self.order_updates.append(stream_data)
                                self._process_order_update(stream_data)

                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    logger.error(f"❌ 处理消息失败: {e}")

        except Exception as e:
            logger.error(f"❌ 监听更新失败: {e}")

    def _process_order_update(self, update_data):
        """处理订单更新"""
        order_data = update_data.get('o', {})

        result = OrderResult(
            order_id=order_data.get('i', ''),
            client_order_id=order_data.get('c', ''),
            symbol=order_data.get('s', ''),
            side=order_data.get('S', ''),
            order_type=order_data.get('o', ''),
            quantity=float(order_data.get('q', 0)),
            filled_quantity=float(order_data.get('z', 0)),
            average_price=float(order_data.get('ap', 0)) if order_data.get('ap') != '0' else 0,
            commission=float(order_data.get('n', 0)),
            status=order_data.get('X', '')
        )

        self.order_results.append(result)

        logger.info(f"📋 订单更新: {result.symbol} {result.side} {result.order_type}")
        logger.info(f"   状态: {result.status}, 成交量: {result.filled_quantity}, 平均价: {result.average_price}")
        logger.info(f"   手续费: {result.commission}")

    def analyze_results(self):
        """分析测试结果"""
        logger.info("\n" + "="*60)
        logger.info("📊 测试结果分析")
        logger.info("="*60)

        # 分析账户快照变化
        if len(self.account_snapshots) >= 2:
            initial = self.account_snapshots[0][1]
            final = self.account_snapshots[-1][1]

            logger.info(f"\n💰 余额变化分析:")
            logger.info(f"   初始余额: {initial.balance:.6f} USDT")
            logger.info(f"   最终余额: {final.balance:.6f} USDT")
            logger.info(f"   余额变化: {final.balance - initial.balance:.6f} USDT")

            logger.info(f"\n💳 可用余额变化:")
            logger.info(f"   初始可用: {initial.available_balance:.6f} USDT")
            logger.info(f"   最终可用: {final.available_balance:.6f} USDT")
            logger.info(f"   可用变化: {final.available_balance - initial.available_balance:.6f} USDT")

            logger.info(f"\n📈 未实现盈亏变化:")
            logger.info(f"   初始未实现盈亏: {initial.unrealized_pnl:.6f} USDT")
            logger.info(f"   最终未实现盈亏: {final.unrealized_pnl:.6f} USDT")
            logger.info(f"   盈亏变化: {final.unrealized_pnl - initial.unrealized_pnl:.6f} USDT")

        # 分析订单执行结果
        logger.info(f"\n📋 订单执行分析:")
        logger.info(f"   总订单数: {len(self.order_results)}")

        total_commission = 0
        for i, result in enumerate(self.order_results):
            logger.info(f"\n   订单 {i+1}:")
            logger.info(f"     符号: {result.symbol}")
            logger.info(f"     方向: {result.side}")
            logger.info(f"     类型: {result.order_type}")
            logger.info(f"     数量: {result.quantity}")
            logger.info(f"     成交量: {result.filled_quantity}")
            logger.info(f"     平均价: {result.average_price}")
            logger.info(f"     手续费: {result.commission}")
            logger.info(f"     状态: {result.status}")

            total_commission += result.commission

            # 验证手续费计算
            if result.filled_quantity > 0 and result.average_price > 0:
                expected_commission = result.filled_quantity * result.average_price * 0.001
                logger.info(f"     预期手续费: {expected_commission:.6f}")
                if abs(result.commission - expected_commission) < 1e-6:
                    logger.info(f"     ✅ 手续费计算正确")
                else:
                    logger.info(f"     ❌ 手续费计算错误")

        logger.info(f"\n💸 总手续费: {total_commission:.6f} USDT")

        # 分析价格数据
        if self.current_prices:
            logger.info(f"\n📊 当前价格数据:")
            for symbol, prices in self.current_prices.items():
                logger.info(f"   {symbol}: bid={prices['bid']}, ask={prices['ask']}, mid={prices['mid']}")

async def run_comprehensive_test():
    """运行全面测试"""
    framework = OrderTestFramework()

    logger.info("🚀 开始全面订单执行测试")
    logger.info("="*60)

    # 1. 连接WebSocket
    if not await framework.connect_websocket():
        return

    # 2. 订阅数据流
    if not await framework.subscribe_to_streams():
        return

    # 3. 拍摄初始账户快照
    initial_snapshot = framework.take_account_snapshot("初始状态")
    if not initial_snapshot:
        return

    # 4. 等待一下获取BBO数据
    logger.info("⏳ 等待BBO数据...")
    await framework.listen_for_updates(3)

    # 5. 执行测试订单序列
    test_orders = [
        TestOrder("BTCUSDT", "BUY", "MARKET", "0.001", client_order_id="test_buy_market_1"),
        TestOrder("BTCUSDT", "SELL", "MARKET", "0.0005", client_order_id="test_sell_market_1"),
        TestOrder("BTCUSDT", "BUY", "MARKET", "0.002", client_order_id="test_buy_market_2"),
        TestOrder("BTCUSDT", "SELL", "MARKET", "0.0015", client_order_id="test_sell_market_2"),
    ]

    for i, order in enumerate(test_orders):
        logger.info(f"\n📝 执行测试订单 {i+1}/{len(test_orders)}")

        # 下单前快照
        pre_order_snapshot = framework.take_account_snapshot(f"订单{i+1}前")

        # 下单
        if await framework.place_order(order):
            # 等待订单执行和更新
            await framework.listen_for_updates(3)

            # 下单后快照
            post_order_snapshot = framework.take_account_snapshot(f"订单{i+1}后")

            # 短暂等待
            await asyncio.sleep(1)

    # 6. 最终快照
    final_snapshot = framework.take_account_snapshot("最终状态")

    # 7. 分析结果
    framework.analyze_results()

    # 8. 关闭连接
    if framework.websocket:
        await framework.websocket.close()

    logger.info("\n✅ 测试完成")

async def run_position_test():
    """运行持仓测试"""
    framework = OrderTestFramework()

    logger.info("🚀 开始持仓管理测试")
    logger.info("="*60)

    # 连接和订阅
    if not await framework.connect_websocket():
        return
    if not await framework.subscribe_to_streams():
        return

    # 初始快照
    framework.take_account_snapshot("持仓测试-初始")
    await framework.listen_for_updates(2)

    # 测试持仓变化序列
    position_orders = [
        TestOrder("BTCUSDT", "BUY", "MARKET", "0.001", client_order_id="pos_test_1"),   # 开多仓
        TestOrder("BTCUSDT", "BUY", "MARKET", "0.001", client_order_id="pos_test_2"),   # 加多仓
        TestOrder("BTCUSDT", "SELL", "MARKET", "0.0005", client_order_id="pos_test_3"), # 减多仓
        TestOrder("BTCUSDT", "SELL", "MARKET", "0.002", client_order_id="pos_test_4"),  # 平多仓+开空仓
        TestOrder("BTCUSDT", "BUY", "MARKET", "0.0015", client_order_id="pos_test_5"),  # 平空仓
    ]

    for i, order in enumerate(position_orders):
        logger.info(f"\n📝 执行持仓测试订单 {i+1}/{len(position_orders)}")

        framework.take_account_snapshot(f"持仓测试-订单{i+1}前")

        if await framework.place_order(order):
            await framework.listen_for_updates(3)
            framework.take_account_snapshot(f"持仓测试-订单{i+1}后")
            await asyncio.sleep(1)

    framework.analyze_results()

    if framework.websocket:
        await framework.websocket.close()

    logger.info("\n✅ 持仓测试完成")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "position":
        asyncio.run(run_position_test())
    else:
        asyncio.run(run_comprehensive_test())
