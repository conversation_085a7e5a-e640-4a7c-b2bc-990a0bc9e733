#!/bin/bash

# 定义每个 session 的名字和要执行的命令
sessions=(
  "s1:cargo run --release --bin backtest -- --config example_config_1.toml"
  "s2:cargo run --release --bin backtest -- --config example_config_2.toml"
  "s3:cargo run --release --bin backtest -- --config example_config_3.toml"
  "s4:cargo run --release --bin backtest -- --config example_config_4.toml"
  "s5:cargo run --release --bin backtest -- --config example_config_5.toml"
)

# 循环创建
for s in "${sessions[@]}"; do
  name="${s%%:*}"
  cmd="${s#*:}"
  tmux new-session -d -s "$name" "$cmd"
done
