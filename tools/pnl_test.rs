use futures::{SinkExt, StreamExt};
use serde_json::json;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

async fn wait_for_order_execution(read: &mut futures::stream::SplitStream<tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>>) -> Result<(), Box<dyn std::error::Error>> {
    for _ in 0..10 {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    if text.contains("ORDER_TRADE_UPDATE") {
                        println!("📋 订单执行: {}", text);
                        return Ok(());
                    }
                },
                _ => {},
            }
        }
    }
    Ok(())
}

async fn check_account_status() -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 检查账户状态...");
    
    let output = std::process::Command::new("curl")
        .args(&["-k", "-s", "https://127.0.0.1:8083/fapi/v1/account"])
        .output()?;
    
    if output.status.success() {
        let response = String::from_utf8_lossy(&output.stdout);
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&response) {
            if let Some(total_balance) = json_value.get("totalWalletBalance") {
                println!("  总余额: {} USDT", total_balance.as_str().unwrap_or("N/A"));
            }
            if let Some(available_balance) = json_value.get("availableBalance") {
                println!("  可用余额: {} USDT", available_balance.as_str().unwrap_or("N/A"));
            }
            if let Some(unrealized_pnl) = json_value.get("totalUnrealizedProfit") {
                println!("  未实现盈亏: {} USDT", unrealized_pnl.as_str().unwrap_or("N/A"));
            }
            
            if let Some(positions) = json_value.get("positions").and_then(|p| p.as_array()) {
                let active_positions: Vec<_> = positions.iter()
                    .filter(|pos| pos.get("positionAmt").and_then(|amt| amt.as_str()).unwrap_or("0") != "0")
                    .collect();
                
                if !active_positions.is_empty() {
                    println!("  活跃持仓:");
                    for pos in active_positions {
                        let symbol = pos.get("symbol").and_then(|s| s.as_str()).unwrap_or("N/A");
                        let amount = pos.get("positionAmt").and_then(|a| a.as_str()).unwrap_or("0");
                        let entry_price = pos.get("entryPrice").and_then(|p| p.as_str()).unwrap_or("0");
                        let unrealized = pos.get("unrealizedProfit").and_then(|u| u.as_str()).unwrap_or("0");
                        println!("    {}: {} @ {} (盈亏: {})", symbol, amount, entry_price, unrealized);
                    }
                } else {
                    println!("  无活跃持仓");
                }
            }
        }
    }
    
    println!("");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始盈亏计算测试");
    println!("测试目标: 验证已实现盈亏和未实现盈亏的计算");
    
    let url = "wss://127.0.0.1:8082";
    
    // 连接WebSocket
    println!("🔗 连接到 {}", url);
    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();
    
    // 订阅订单更新
    let subscribe_msg = json!({
        "method": "SUBSCRIBE",
        "params": ["test_listen_key"],
        "id": 1
    });
    
    write.send(Message::Text(subscribe_msg.to_string())).await?;
    if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
        let _ = msg?;
    }
    
    // 检查初始状态
    println!("\n📊 初始账户状态:");
    check_account_status().await?;
    
    println!("📝 步骤1: 开多仓 (买入 0.002 BTC) - 建立较大持仓以观察盈亏变化");
    let order1 = json!({
        "id": 301,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.002",
            "newClientOrderId": "pnl_test_open"
        }
    });
    
    write.send(Message::Text(order1.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    println!("\n📊 开仓后账户状态:");
    check_account_status().await?;
    
    // 等待一些时间让价格可能发生变化
    println!("⏳ 等待5秒观察价格变化对未实现盈亏的影响...");
    tokio::time::sleep(Duration::from_secs(5)).await;
    
    println!("\n📊 等待后账户状态:");
    check_account_status().await?;
    
    println!("📝 步骤2: 部分平仓 (卖出 0.001 BTC) - 测试已实现盈亏计算");
    let order2 = json!({
        "id": 302,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "pnl_test_partial_close"
        }
    });
    
    write.send(Message::Text(order2.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    println!("\n📊 部分平仓后账户状态:");
    check_account_status().await?;
    
    println!("📝 步骤3: 完全平仓 (卖出剩余 0.001 BTC)");
    let order3 = json!({
        "id": 303,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "pnl_test_full_close"
        }
    });
    
    write.send(Message::Text(order3.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    println!("\n📊 完全平仓后账户状态:");
    check_account_status().await?;
    
    println!("🔌 关闭连接");
    write.close().await?;
    
    println!("\n✅ 盈亏计算测试完成");
    println!("💡 请观察上述输出中的盈亏变化是否符合预期");
    
    Ok(())
}
