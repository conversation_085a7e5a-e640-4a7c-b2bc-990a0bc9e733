use futures::{SinkExt, StreamExt};
use serde_json::json;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

async fn wait_for_order_execution(
    read: &mut futures::stream::SplitStream<
        tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>,
    >,
) -> Result<(), Box<dyn std::error::Error>> {
    for _ in 0..10 {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    if text.contains("ORDER_TRADE_UPDATE") {
                        println!("📋 订单执行: {}", text);
                        return Ok(());
                    }
                }
                _ => {}
            }
        }
    }
    Ok(())
}

async fn check_account_status() -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 检查账户状态...");
    
    let output = std::process::Command::new("curl")
        .args(&["-k", "-s", "https://127.0.0.1:8083/fapi/v1/account"])
        .output()?;
    
    if output.status.success() {
        let response = String::from_utf8_lossy(&output.stdout);
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&response) {
            if let Some(total_balance) = json_value.get("totalWalletBalance") {
                println!("  总余额: {} USDT", total_balance.as_str().unwrap_or("N/A"));
            }
            if let Some(available_balance) = json_value.get("availableBalance") {
                println!("  可用余额: {} USDT", available_balance.as_str().unwrap_or("N/A"));
            }
            if let Some(unrealized_pnl) = json_value.get("totalUnrealizedProfit") {
                println!("  未实现盈亏: {} USDT", unrealized_pnl.as_str().unwrap_or("N/A"));
            }
            
            if let Some(positions) = json_value.get("positions").and_then(|p| p.as_array()) {
                let active_positions: Vec<_> = positions.iter()
                    .filter(|pos| pos.get("positionAmt").and_then(|amt| amt.as_str()).unwrap_or("0") != "0")
                    .collect();
                
                if !active_positions.is_empty() {
                    println!("  活跃持仓:");
                    for pos in active_positions {
                        let symbol = pos.get("symbol").and_then(|s| s.as_str()).unwrap_or("N/A");
                        let amount = pos.get("positionAmt").and_then(|a| a.as_str()).unwrap_or("0");
                        let entry_price = pos.get("entryPrice").and_then(|p| p.as_str()).unwrap_or("0");
                        let unrealized = pos.get("unrealizedProfit").and_then(|u| u.as_str()).unwrap_or("0");
                        println!("    {}: {} @ {} (盈亏: {})", symbol, amount, entry_price, unrealized);
                    }
                } else {
                    println!("  无活跃持仓");
                }
            }
        }
    }
    
    println!("");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始Bug修复验证测试");
    println!("测试目标: 验证手续费率、持仓精度和平仓逻辑修复");
    
    let url = "wss://127.0.0.1:8082";
    
    // 连接WebSocket
    println!("🔗 连接到 {}", url);
    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();
    
    // 订阅订单更新
    let subscribe_msg = json!({
        "method": "SUBSCRIBE",
        "params": ["test_listen_key"],
        "id": 1
    });
    
    write.send(Message::Text(subscribe_msg.to_string())).await?;
    if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
        let _ = msg?;
    }
    
    // 检查初始状态
    println!("\n📊 初始账户状态:");
    check_account_status().await?;
    
    // 测试1: 验证手续费率修复
    println!("\n🧪 测试1: 手续费率修复验证");
    println!("执行一笔交易，验证手续费是否按照配置的0.04%计算");
    
    let order1 = json!({
        "id": 401,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "fee_test_1"
        }
    });
    
    write.send(Message::Text(order1.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    println!("\n📊 交易后账户状态:");
    check_account_status().await?;
    
    // 测试2: 验证平仓逻辑修复
    println!("\n🧪 测试2: 平仓逻辑修复验证");
    println!("执行完全平仓操作，验证持仓是否正确清零");
    
    let order2 = json!({
        "id": 402,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "close_test_1"
        }
    });
    
    write.send(Message::Text(order2.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    println!("\n📊 平仓后账户状态:");
    check_account_status().await?;
    
    // 测试3: 验证精度问题修复
    println!("\n🧪 测试3: 持仓精度问题修复验证");
    println!("执行多次开平仓操作，验证持仓是否正确处理");
    
    // 开仓
    let order3 = json!({
        "id": 403,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.0005",
            "newClientOrderId": "precision_test_1"
        }
    });
    
    write.send(Message::Text(order3.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    // 平仓
    let order4 = json!({
        "id": 404,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.0005",
            "newClientOrderId": "precision_test_2"
        }
    });
    
    write.send(Message::Text(order4.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    
    println!("\n📊 最终账户状态:");
    check_account_status().await?;
    
    println!("🔌 关闭连接");
    write.close().await?;
    
    println!("\n✅ Bug修复验证测试完成");
    
    Ok(())
}
