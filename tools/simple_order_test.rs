use futures::{SinkExt, StreamExt};
use serde_json::json;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始持仓管理测试");

    let url = "wss://127.0.0.1:8082";

    // 连接WebSocket
    println!("🔗 连接到 {}", url);
    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // 订阅订单更新
    let subscribe_msg = json!({
        "method": "SUBSCRIBE",
        "params": ["test_listen_key"],
        "id": 1
    });

    println!("📤 发送订阅消息: {}", subscribe_msg);
    write.send(Message::Text(subscribe_msg.to_string())).await?;

    // 等待订阅确认
    if let Ok(Some(msg)) = timeout(Duration::from_secs(5), read.next()).await {
        match msg? {
            Message::Text(text) => println!("📥 订阅响应: {}", text),
            _ => println!("📥 收到非文本消息"),
        }
    }

    // 订阅BBO数据
    let bbo_subscribe_msg = json!({
        "method": "SUBSCRIBE",
        "params": ["btcusdt@bookTicker"],
        "id": 2
    });

    println!("📤 发送BBO订阅: {}", bbo_subscribe_msg);
    write
        .send(Message::Text(bbo_subscribe_msg.to_string()))
        .await?;

    // 等待BBO订阅确认
    if let Ok(Some(msg)) = timeout(Duration::from_secs(5), read.next()).await {
        match msg? {
            Message::Text(text) => println!("📥 BBO订阅响应: {}", text),
            _ => println!("📥 收到非文本消息"),
        }
    }

    // 等待一些BBO数据
    println!("⏳ 等待BBO数据...");
    for _ in 0..3 {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    if text.contains("bookTicker") {
                        println!("📊 收到BBO数据: {}", text);
                    }
                }
                _ => {}
            }
        }
    }

    // 下第一个测试订单
    let order_msg = json!({
        "id": 123,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "test_order_001"
        }
    });

    println!("📝 发送买入订单: {}", order_msg);
    write.send(Message::Text(order_msg.to_string())).await?;

    // 等待订单响应和更新
    println!("⏳ 等待订单执行...");
    for _ in 0..10 {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(2), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    println!("📥 收到消息: {}", text);
                    if text.contains("ORDER_TRADE_UPDATE") {
                        println!("✅ 订单执行完成");
                        break;
                    }
                }
                _ => {}
            }
        }
    }

    // 等待一下再下第二个订单
    tokio::time::sleep(Duration::from_secs(2)).await;

    // 下第二个测试订单（卖出）
    let sell_order_msg = json!({
        "id": 124,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.0005",
            "newClientOrderId": "test_order_002"
        }
    });

    println!("📝 发送卖出订单: {}", sell_order_msg);
    write
        .send(Message::Text(sell_order_msg.to_string()))
        .await?;

    // 等待第二个订单执行
    println!("⏳ 等待第二个订单执行...");
    for _ in 0..10 {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(2), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    println!("📥 收到消息: {}", text);
                    if text.contains("ORDER_TRADE_UPDATE") {
                        println!("✅ 第二个订单执行完成");
                        break;
                    }
                }
                _ => {}
            }
        }
    }

    println!("🔌 关闭连接");
    write.close().await?;

    println!("✅ 测试完成");
    println!("💡 请运行 './manual_order_test.sh check' 检查账户状态变化");

    Ok(())
}
