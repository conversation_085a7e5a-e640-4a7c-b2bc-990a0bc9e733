use futures::{SinkExt, StreamExt};
use serde_json::json;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

async fn wait_for_order_execution(
    read: &mut futures::stream::SplitStream<
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
    >,
) -> Result<(), Box<dyn std::error::Error>> {
    for _ in 0..10 {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    if text.contains("ORDER_TRADE_UPDATE") {
                        println!("📋 订单执行: {}", text);
                        return Ok(());
                    }
                }
                _ => {}
            }
        }
    }
    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始持仓管理测试");
    println!("测试序列: 开仓 -> 加仓 -> 减仓 -> 平仓 -> 反向开仓");

    let url = "wss://127.0.0.1:8082";

    // 连接WebSocket
    println!("🔗 连接到 {}", url);
    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // 订阅订单更新
    let subscribe_msg = json!({
        "method": "SUBSCRIBE",
        "params": ["test_listen_key"],
        "id": 1
    });

    write.send(Message::Text(subscribe_msg.to_string())).await?;
    if let Ok(Some(msg)) = timeout(Duration::from_secs(3), read.next()).await {
        let _ = msg?;
    }

    println!("\n📝 步骤1: 开多仓 (买入 0.001 BTC)");
    let order1 = json!({
        "id": 201,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "pos_test_open_long"
        }
    });

    write.send(Message::Text(order1.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    tokio::time::sleep(Duration::from_secs(2)).await;

    println!("\n📝 步骤2: 加多仓 (再买入 0.001 BTC)");
    let order2 = json!({
        "id": 202,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.001",
            "newClientOrderId": "pos_test_add_long"
        }
    });

    write.send(Message::Text(order2.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    tokio::time::sleep(Duration::from_secs(2)).await;

    println!("\n📝 步骤3: 减多仓 (卖出 0.0005 BTC)");
    let order3 = json!({
        "id": 203,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.0005",
            "newClientOrderId": "pos_test_reduce_long"
        }
    });

    write.send(Message::Text(order3.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    tokio::time::sleep(Duration::from_secs(2)).await;

    println!("\n📝 步骤4: 平多仓+开空仓 (卖出 0.002 BTC)");
    let order4 = json!({
        "id": 204,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "SELL",
            "type": "MARKET",
            "quantity": "0.002",
            "newClientOrderId": "pos_test_close_open_short"
        }
    });

    write.send(Message::Text(order4.to_string())).await?;
    wait_for_order_execution(&mut read).await?;
    tokio::time::sleep(Duration::from_secs(2)).await;

    println!("\n📝 步骤5: 平空仓 (买入 0.0005 BTC)");
    let order5 = json!({
        "id": 205,
        "method": "order.place",
        "params": {
            "symbol": "BTCUSDT",
            "side": "BUY",
            "type": "MARKET",
            "quantity": "0.0005",
            "newClientOrderId": "pos_test_close_short"
        }
    });

    write.send(Message::Text(order5.to_string())).await?;
    wait_for_order_execution(&mut read).await?;

    println!("🔌 关闭连接");
    write.close().await?;

    println!("\n✅ 持仓管理测试完成");
    println!("💡 请运行 './manual_order_test.sh check' 检查最终账户状态");

    Ok(())
}
