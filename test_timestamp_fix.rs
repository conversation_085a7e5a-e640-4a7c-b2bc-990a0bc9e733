use backtest::account::types::TradeRecord;
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use tokio::sync::Mutex;

/// 测试时间戳修复
#[tokio::test]
async fn test_timestamp_fix() {
    println!("🧪 测试时间戳修复");
    println!("==================");

    // 1. 创建市场数据时间戳
    let market_timestamp = ****************; // 2022-01-01 00:00:00 UTC (微秒)
    let market_datetime = DateTime::from_timestamp_micros(market_timestamp as i64).unwrap();

    println!("📊 市场数据时间戳: {}", market_timestamp);
    println!("📅 市场数据时间: {}", market_datetime);

    // 2. 创建BBO数据
    let bbo = Bbo {
        update_id: 1,
        bid_price: Price::new(50000.0),
        bid_quantity: 1.0,
        ask_price: Price::new(50001.0),
        ask_quantity: 1.0,
        timestamp: Some(market_timestamp),
        data_source_type: backtest::config::DataSourceType::BinanceTardis,
    };

    // 3. 创建订单（使用市场数据时间戳）
    let order = Order {
        id: "test_order_1".to_string(),
        client_order_id: "client_test_1".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50002.0)),
        quantity: 0.1,
        status: OrderStatus::Pending,
        timestamp: market_datetime, // 使用市场数据时间戳
        execution_info: None,
    };

    println!("📝 订单时间戳: {}", order.timestamp);

    // 4. 验证时间戳一致性
    assert_eq!(
        order.timestamp, market_datetime,
        "订单时间戳应该与市场数据时间戳一致"
    );

    // 5. 验证时间戳转换
    let converted_timestamp = order.timestamp.timestamp_micros() as u64;
    assert_eq!(converted_timestamp, market_timestamp, "时间戳转换应该正确");

    println!("✅ 时间戳修复验证通过");
    println!("   - 市场数据时间戳: {}", market_timestamp);
    println!("   - 订单时间戳: {}", order.timestamp);
    println!("   - 转换后时间戳: {}", converted_timestamp);
}

/// 测试交易记录时间戳修复
#[tokio::test]
async fn test_trade_record_timestamp_fix() {
    println!("🧪 测试交易记录时间戳修复");
    println!("==========================");

    // 1. 创建市场数据时间戳
    let market_timestamp = ****************; // 2022-01-01 00:00:00 UTC (微秒)
    let market_datetime = DateTime::from_timestamp_micros(market_timestamp as i64).unwrap();

    println!("📊 市场数据时间戳: {}", market_timestamp);
    println!("📅 市场数据时间: {}", market_datetime);

    // 2. 创建Trade（使用市场数据时间戳）
    let trade = Trade {
        id: "test_trade_1".to_string(),
        symbol: "BTCUSDT".to_string(),
        price: Price::new(50000.0),
        quantity: 0.1,
        side: OrderSide::Buy,
        timestamp: Some(market_datetime), // 使用市场数据时间戳
        data_source_type: backtest::config::DataSourceType::BinanceTardis,
    };

    println!("💰 Trade时间戳: {:?}", trade.timestamp);

    // 3. 创建TradeRecord（使用Trade中的时间戳）
    let trade_record = TradeRecord::new_with_timestamp(
        trade.id.clone(),
        "test_order_1".to_string(),
        trade.symbol.clone(),
        trade.side,
        trade.price,
        trade.quantity,
        0.1, // 手续费
        "USDT".to_string(),
        true,                     // is_maker
        trade.timestamp.unwrap(), // 使用Trade中的时间戳
    );

    println!("📋 TradeRecord时间戳: {}", trade_record.timestamp);

    // 4. 验证时间戳一致性
    assert_eq!(
        trade_record.timestamp, market_datetime,
        "TradeRecord时间戳应该与市场数据时间戳一致"
    );

    // 5. 验证时间戳转换
    let converted_timestamp = trade_record.timestamp.timestamp_micros() as u64;
    assert_eq!(converted_timestamp, market_timestamp, "时间戳转换应该正确");

    println!("✅ 交易记录时间戳修复验证通过");
    println!("   - 市场数据时间戳: {}", market_timestamp);
    println!("   - Trade时间戳: {:?}", trade.timestamp);
    println!("   - TradeRecord时间戳: {}", trade_record.timestamp);
    println!("   - 转换后时间戳: {}", converted_timestamp);
}
