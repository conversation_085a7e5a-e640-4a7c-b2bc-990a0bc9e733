#!/bin/bash

# TLS证书问题修复脚本
# 解决 "unable to get local issuer certificate" 错误

set -e

CERT_DIR="./certs"
CA_CERT="$CERT_DIR/ca.crt"
SERVER_CERT="$CERT_DIR/server.crt"
SERVER_KEY="$CERT_DIR/server.key"
BUNDLE_CERT="$CERT_DIR/ca-bundle.crt"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  TLS证书问题修复脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 检查证书文件是否存在
check_certificates() {
    echo -e "${YELLOW}1. 检查证书文件...${NC}"
    
    if [[ ! -f "$CA_CERT" ]]; then
        echo -e "${RED}❌ CA证书文件不存在: $CA_CERT${NC}"
        echo -e "${YELLOW}正在生成新的CA证书...${NC}"
        ./generate_ca_certs.sh
    else
        echo -e "${GREEN}✓ CA证书文件存在: $CA_CERT${NC}"
    fi
    
    if [[ ! -f "$SERVER_CERT" ]]; then
        echo -e "${RED}❌ 服务器证书文件不存在: $SERVER_CERT${NC}"
        exit 1
    else
        echo -e "${GREEN}✓ 服务器证书文件存在: $SERVER_CERT${NC}"
    fi
    
    if [[ ! -f "$SERVER_KEY" ]]; then
        echo -e "${RED}❌ 服务器私钥文件不存在: $SERVER_KEY${NC}"
        exit 1
    else
        echo -e "${GREEN}✓ 服务器私钥文件存在: $SERVER_KEY${NC}"
    fi
    echo
}

# 验证证书有效性
verify_certificates() {
    echo -e "${YELLOW}2. 验证证书有效性...${NC}"
    
    # 验证CA证书
    if openssl x509 -in "$CA_CERT" -text -noout > /dev/null 2>&1; then
        echo -e "${GREEN}✓ CA证书格式正确${NC}"
    else
        echo -e "${RED}❌ CA证书格式错误${NC}"
        exit 1
    fi
    
    # 验证服务器证书
    if openssl x509 -in "$SERVER_CERT" -text -noout > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 服务器证书格式正确${NC}"
    else
        echo -e "${RED}❌ 服务器证书格式错误${NC}"
        exit 1
    fi
    
    # 验证证书链
    if openssl verify -CAfile "$CA_CERT" "$SERVER_CERT" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 证书链验证成功${NC}"
    else
        echo -e "${RED}❌ 证书链验证失败${NC}"
        echo -e "${YELLOW}服务器证书不是由CA证书签发的${NC}"
    fi
    echo
}

# 创建证书包
create_certificate_bundle() {
    echo -e "${YELLOW}3. 创建证书包...${NC}"
    
    # 创建包含CA证书的证书包
    cp "$CA_CERT" "$BUNDLE_CERT"
    
    # 如果系统证书存在，也添加到包中
    if [[ -f "/etc/ssl/certs/ca-certificates.crt" ]]; then
        echo -e "${BLUE}添加系统证书到证书包...${NC}"
        cat "/etc/ssl/certs/ca-certificates.crt" >> "$BUNDLE_CERT"
    elif [[ -f "/etc/ssl/cert.pem" ]]; then
        echo -e "${BLUE}添加系统证书到证书包...${NC}"
        cat "/etc/ssl/cert.pem" >> "$BUNDLE_CERT"
    fi
    
    echo -e "${GREEN}✓ 证书包创建完成: $BUNDLE_CERT${NC}"
    echo
}

# 系统级别安装CA证书
install_system_ca() {
    echo -e "${YELLOW}4. 系统级别安装CA证书...${NC}"
    
    if [[ "$EUID" -eq 0 ]]; then
        # 以root权限运行
        if [[ -d "/usr/local/share/ca-certificates" ]]; then
            # Ubuntu/Debian系统
            cp "$CA_CERT" "/usr/local/share/ca-certificates/backtest-ca.crt"
            update-ca-certificates
            echo -e "${GREEN}✓ CA证书已安装到系统证书存储 (Ubuntu/Debian)${NC}"
        elif [[ -d "/etc/pki/ca-trust/source/anchors" ]]; then
            # CentOS/RHEL系统
            cp "$CA_CERT" "/etc/pki/ca-trust/source/anchors/backtest-ca.crt"
            update-ca-trust
            echo -e "${GREEN}✓ CA证书已安装到系统证书存储 (CentOS/RHEL)${NC}"
        else
            echo -e "${YELLOW}⚠ 未识别的Linux发行版，跳过系统证书安装${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 需要root权限来安装系统证书${NC}"
        echo -e "${BLUE}如需安装到系统，请运行: sudo $0${NC}"
    fi
    echo
}

# 设置环境变量
setup_environment() {
    echo -e "${YELLOW}5. 设置环境变量...${NC}"
    
    # 创建环境变量设置脚本
    cat > "$CERT_DIR/setup_env.sh" << EOF
#!/bin/bash
# TLS证书环境变量设置

export SSL_CERT_FILE="$(pwd)/$BUNDLE_CERT"
export SSL_CERT_DIR="$(pwd)/$CERT_DIR"
export REQUESTS_CA_BUNDLE="$(pwd)/$BUNDLE_CERT"
export CURL_CA_BUNDLE="$(pwd)/$BUNDLE_CERT"

echo "TLS证书环境变量已设置:"
echo "  SSL_CERT_FILE=\$SSL_CERT_FILE"
echo "  SSL_CERT_DIR=\$SSL_CERT_DIR"
echo "  REQUESTS_CA_BUNDLE=\$REQUESTS_CA_BUNDLE"
echo "  CURL_CA_BUNDLE=\$CURL_CA_BUNDLE"
EOF
    
    chmod +x "$CERT_DIR/setup_env.sh"
    echo -e "${GREEN}✓ 环境变量设置脚本创建: $CERT_DIR/setup_env.sh${NC}"
    echo -e "${BLUE}使用方法: source $CERT_DIR/setup_env.sh${NC}"
    echo
}

# 测试连接
test_connections() {
    echo -e "${YELLOW}6. 测试连接...${NC}"
    
    # 测试HTTPS连接
    echo -e "${BLUE}测试HTTPS连接...${NC}"
    if curl -s --cacert "$CA_CERT" https://localhost:8083/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ HTTPS连接测试成功${NC}"
    else
        echo -e "${YELLOW}⚠ HTTPS连接测试失败（服务器可能未运行）${NC}"
    fi
    
    # 测试WSS连接
    echo -e "${BLUE}测试WSS连接...${NC}"
    if command -v cargo > /dev/null 2>&1; then
        if RUST_LOG=error cargo run --bin test_ca_client > /dev/null 2>&1; then
            echo -e "${GREEN}✓ WSS连接测试成功${NC}"
        else
            echo -e "${YELLOW}⚠ WSS连接测试失败（服务器可能未运行）${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 未找到cargo，跳过WSS测试${NC}"
    fi
    echo
}

# 生成客户端配置示例
generate_client_examples() {
    echo -e "${YELLOW}7. 生成客户端配置示例...${NC}"
    
    # Rust客户端示例
    cat > "$CERT_DIR/rust_client_example.rs" << 'EOF'
// Rust客户端TLS配置示例

use native_tls::{TlsConnector, Certificate};
use std::fs;

fn create_tls_connector() -> Result<TlsConnector, Box<dyn std::error::Error>> {
    // 方案1: 使用CA证书
    let ca_cert_data = fs::read("./certs/ca.crt")?;
    let ca_cert = Certificate::from_pem(&ca_cert_data)?;
    
    let connector = TlsConnector::builder()
        .add_root_certificate(ca_cert)
        .build()?;
    
    Ok(connector)
}

fn create_insecure_connector() -> Result<TlsConnector, Box<dyn std::error::Error>> {
    // 方案2: 跳过证书验证（仅用于开发测试）
    let connector = TlsConnector::builder()
        .danger_accept_invalid_certs(true)
        .danger_accept_invalid_hostnames(true)
        .build()?;
    
    Ok(connector)
}
EOF
    
    # Python客户端示例
    cat > "$CERT_DIR/python_client_example.py" << 'EOF'
# Python客户端TLS配置示例

import ssl
import websocket

def create_ssl_context():
    """创建SSL上下文，使用CA证书"""
    ssl_context = ssl.create_default_context()
    ssl_context.load_verify_locations('./certs/ca.crt')
    return ssl_context

def create_insecure_ssl_context():
    """创建不验证证书的SSL上下文（仅用于开发测试）"""
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    return ssl_context

# 使用示例
if __name__ == "__main__":
    # 方案1: 使用CA证书
    ssl_context = create_ssl_context()
    ws = websocket.WebSocket(sslopt={"context": ssl_context})
    
    # 方案2: 跳过证书验证
    # ssl_context = create_insecure_ssl_context()
    # ws = websocket.WebSocket(sslopt={"context": ssl_context})
EOF
    
    # JavaScript/Node.js客户端示例
    cat > "$CERT_DIR/nodejs_client_example.js" << 'EOF'
// Node.js客户端TLS配置示例

const WebSocket = require('ws');
const fs = require('fs');
const https = require('https');

// 方案1: 使用CA证书
function createSecureWebSocket() {
    const caCert = fs.readFileSync('./certs/ca.crt');
    
    const agent = new https.Agent({
        ca: caCert
    });
    
    const ws = new WebSocket('wss://localhost:8082', {
        agent: agent
    });
    
    return ws;
}

// 方案2: 跳过证书验证（仅用于开发测试）
function createInsecureWebSocket() {
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    
    const ws = new WebSocket('wss://localhost:8082');
    
    return ws;
}
EOF
    
    echo -e "${GREEN}✓ 客户端配置示例已生成:${NC}"
    echo -e "${BLUE}  - Rust: $CERT_DIR/rust_client_example.rs${NC}"
    echo -e "${BLUE}  - Python: $CERT_DIR/python_client_example.py${NC}"
    echo -e "${BLUE}  - Node.js: $CERT_DIR/nodejs_client_example.js${NC}"
    echo
}

# 显示解决方案总结
show_summary() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}  修复完成！解决方案总结${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo
    echo -e "${YELLOW}问题原因:${NC}"
    echo -e "  客户端无法找到用于验证服务器证书的CA证书"
    echo
    echo -e "${YELLOW}解决方案:${NC}"
    echo -e "${GREEN}1. 客户端代码中添加CA证书${NC}"
    echo -e "   - 使用 $CA_CERT"
    echo -e "   - 参考 $CERT_DIR/*_client_example.*"
    echo
    echo -e "${GREEN}2. 使用环境变量${NC}"
    echo -e "   - 运行: source $CERT_DIR/setup_env.sh"
    echo
    echo -e "${GREEN}3. 系统级别安装CA证书${NC}"
    echo -e "   - 运行: sudo $0"
    echo
    echo -e "${GREEN}4. 临时跳过证书验证（仅开发测试）${NC}"
    echo -e "   - 参考客户端示例中的 insecure 方法"
    echo
    echo -e "${YELLOW}测试命令:${NC}"
    echo -e "  - HTTPS: curl --cacert $CA_CERT https://localhost:8083/api/v1/health"
    echo -e "  - WSS: cargo run --bin test_ca_client"
    echo
}

# 主函数
main() {
    check_certificates
    verify_certificates
    create_certificate_bundle
    install_system_ca
    setup_environment
    test_connections
    generate_client_examples
    show_summary
}

# 运行主函数
main
