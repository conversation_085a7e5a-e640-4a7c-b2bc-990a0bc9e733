[./arch.png](./arch.png)

模块介绍
全局配置
一个线程安全的singleton，可以全局读写
- 交易所名称
- 回测时间区间
- 历史数据路径
- ...
历史数据读取模块
- 根据全局配置读取数据到内存
- 以带有时间戳的行情为时间屏障，其他数据是否输出给下游模块要依据这个时间戳
- 如果没有时间戳，则以 update_id 为屏障，比如bbo的update_id
- Trade 和 bbo都没有时间戳，如何对齐？
撮合引擎
- 所有的行情数据全部都先到撮合引擎，防止时间不一致问题，模拟交易所真实情况
- 撮合引擎会自动构建orderbook，用于撮合
- 使用bbo进行撮合
T0  ask: 100, bid:  99
T1  挂 BUY 99.5 -> 加入 bids 树 [99.5]
T2  后续 SELL_MARKET 99.3 到达 → price ≤ 99.5
    → 撮合顺序：99.5 挂单先被吃，成交价=99.5(取挂单价)
T3  depth 更新：ask 降至 99.4, bid 不变
    → 如未完全成交，剩余 BUY 99.5 量在 T3 再次撮合
- 如何模拟下单对orderbook的冲击？
  - 撮合不影响orderbook，如果来了新的orderbook_snapshot，则重置orderbook
Websocket api
- 管理用户订阅
- 根据链接记录用户订阅的数据类型，如果没有订阅则丢弃
Http api
- 获取各个模块的snapshot
技术指标
技术实现
- 异步框架：tokio
- Websocket：tokio-tungstenite
- Http：warp
- 日志：tracing
- 模块间通信
  - tokio::broadcast & tokio::mpsc
  - 每个模块一个input channel、一个output channel
性能预期
第一版预期不高，500us内完成一个请求即可
