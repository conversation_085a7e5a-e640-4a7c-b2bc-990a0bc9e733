use backtest::config::OrderLatencyConfig;
use backtest::matching::OrderLatencySimulator;
use backtest::types::{Order, OrderSide, OrderStatus, OrderType, Price};
use chrono::Utc;
use std::time::Instant;
use tracing::info;

/// 创建测试订单
fn create_test_order(id: &str, side: OrderSide, price: Option<f64>, quantity: f64) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type: if price.is_some() {
            OrderType::Limit
        } else {
            OrderType::Market
        },
        side,
        price: price.map(Price::new),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
    }
}

#[tokio::test]
async fn test_order_latency_direct_timing() {
    tracing_subscriber::fmt::init();
    info!("🚀 开始直接延迟时间验证测试");

    // 配置3ms延迟
    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let mut simulator = OrderLatencySimulator::new(config);

    // 基准时间戳（微秒）
    let base_timestamp = 1000000; // 1秒

    // 添加订单
    let order = create_test_order("test_order", OrderSide::Buy, Some(50000.0), 1.0);
    let order_id = order.id.clone();

    info!("📝 添加订单到延迟队列，基准时间戳: {}", base_timestamp);
    simulator.add_order(order, base_timestamp).unwrap();

    // 检查队列状态
    let stats = simulator.get_stats();
    info!(
        "📊 队列统计: 大小={}, 延迟={}微秒",
        stats.queue_size, stats.latency_micros
    );
    assert_eq!(stats.queue_size, 1, "队列中应该有1个订单");

    // 检查下一个执行时间戳
    let next_execution = simulator.next_execution_timestamp().unwrap();
    let expected_execution = base_timestamp + 3000; // 3ms后
    info!(
        "⏰ 预期执行时间戳: {}, 实际: {}",
        expected_execution, next_execution
    );
    assert_eq!(
        next_execution, expected_execution,
        "执行时间戳应该是基准时间+3ms"
    );

    // 测试在延迟时间之前，订单不应该可执行
    let early_timestamp = base_timestamp + 2000; // 2ms后
    let ready_orders = simulator.get_ready_orders(early_timestamp);
    info!("🔍 2ms后检查: 可执行订单数={}", ready_orders.len());
    assert_eq!(ready_orders.len(), 0, "2ms后不应该有可执行订单");

    // 测试在延迟时间之后，订单应该可执行
    let execution_timestamp = base_timestamp + 3000; // 3ms后
    let ready_orders = simulator.get_ready_orders(execution_timestamp);
    info!("✅ 3ms后检查: 可执行订单数={}", ready_orders.len());
    assert_eq!(ready_orders.len(), 1, "3ms后应该有1个可执行订单");
    assert_eq!(ready_orders[0].id, order_id, "执行的订单ID应该匹配");

    // 检查队列现在应该为空
    let final_stats = simulator.get_stats();
    info!("📊 最终队列统计: 大小={}", final_stats.queue_size);
    assert_eq!(final_stats.queue_size, 0, "执行后队列应该为空");

    info!("✅ 直接延迟时间验证测试完成");
}

#[tokio::test]
async fn test_order_latency_multiple_orders_timing() {
    info!("🚀 开始多订单延迟时间验证测试");

    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 2000, // 2ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let mut simulator = OrderLatencySimulator::new(config);
    let base_timestamp = 2000000; // 2秒基准

    // 添加3个订单，间隔1ms
    for i in 0..3 {
        let order = create_test_order(
            &format!("order_{}", i + 1),
            OrderSide::Buy,
            Some(50000.0),
            1.0,
        );
        let submit_timestamp = base_timestamp + (i * 1000); // 每个订单间隔1ms
        simulator.add_order(order, submit_timestamp).unwrap();
        info!("📝 添加订单{} 在时间戳: {}", i + 1, submit_timestamp);
    }

    // 检查队列大小
    assert_eq!(simulator.queue_size(), 3, "应该有3个订单在队列中");

    // 模拟时间推进，检查订单执行顺序
    let mut executed_orders = Vec::new();

    // 在第一个订单的执行时间 (2000000 + 2000 = 2002000)
    let check_time_1 = base_timestamp + 2000;
    let ready = simulator.get_ready_orders(check_time_1);
    info!("⏰ 时间戳 {} 可执行订单: {}", check_time_1, ready.len());
    executed_orders.extend(ready);

    // 在第二个订单的执行时间 (2001000 + 2000 = 2003000)
    let check_time_2 = base_timestamp + 3000;
    let ready = simulator.get_ready_orders(check_time_2);
    info!("⏰ 时间戳 {} 可执行订单: {}", check_time_2, ready.len());
    executed_orders.extend(ready);

    // 在第三个订单的执行时间 (2002000 + 2000 = 2004000)
    let check_time_3 = base_timestamp + 4000;
    let ready = simulator.get_ready_orders(check_time_3);
    info!("⏰ 时间戳 {} 可执行订单: {}", check_time_3, ready.len());
    executed_orders.extend(ready);

    // 验证执行顺序
    assert_eq!(executed_orders.len(), 3, "应该执行3个订单");
    assert_eq!(
        executed_orders[0].id, "order_1",
        "第一个执行的应该是order_1"
    );
    assert_eq!(
        executed_orders[1].id, "order_2",
        "第二个执行的应该是order_2"
    );
    assert_eq!(
        executed_orders[2].id, "order_3",
        "第三个执行的应该是order_3"
    );

    // 队列应该为空
    assert_eq!(simulator.queue_size(), 0, "所有订单执行后队列应该为空");

    info!("✅ 多订单延迟时间验证测试完成");
}

#[tokio::test]
async fn test_order_latency_random_timing() {
    info!("🚀 开始随机延迟时间验证测试");

    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 1000, // 1ms基础延迟
        max_queue_size: 1000,
        random_latency: true, // 启用随机延迟 (800-1200微秒)
    };

    let mut simulator = OrderLatencySimulator::new(config);
    let base_timestamp = 3000000;

    // 添加多个订单测试随机延迟
    let mut execution_timestamps = Vec::new();

    for i in 0..10 {
        let order = create_test_order(
            &format!("random_order_{}", i),
            OrderSide::Buy,
            Some(50000.0),
            0.1,
        );
        simulator.add_order(order, base_timestamp).unwrap();
    }

    // 获取所有订单的执行时间戳
    let stats = simulator.get_stats();
    info!("📊 添加10个订单后队列大小: {}", stats.queue_size);

    // 通过逐步推进时间来获取所有执行时间戳
    for check_time in (base_timestamp + 800)..=(base_timestamp + 1200) {
        let ready_orders = simulator.get_ready_orders(check_time);
        for _ in ready_orders {
            execution_timestamps.push(check_time);
        }
        if execution_timestamps.len() >= 10 {
            break;
        }
    }

    info!("📊 随机延迟执行时间戳: {:?}", execution_timestamps);

    // 验证随机延迟范围
    for &exec_time in &execution_timestamps {
        let delay = exec_time - base_timestamp;
        assert!(
            delay >= 800 && delay <= 1200,
            "随机延迟 {} 应该在800-1200微秒范围内",
            delay
        );
        info!("✓ 延迟 {} 微秒在有效范围内", delay);
    }

    // 检查是否真的有随机性（不是所有延迟都相同）
    let unique_delays: std::collections::HashSet<_> = execution_timestamps
        .iter()
        .map(|&t| t - base_timestamp)
        .collect();

    info!("📈 唯一延迟值数量: {}", unique_delays.len());
    assert!(
        unique_delays.len() > 1,
        "应该有多个不同的延迟值，证明随机性"
    );

    info!("✅ 随机延迟时间验证测试完成");
}

#[tokio::test]
async fn test_order_latency_performance() {
    info!("🚀 开始延迟模拟器性能测试");

    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 1000,
        max_queue_size: 10000,
        random_latency: false,
    };

    let mut simulator = OrderLatencySimulator::new(config);
    let base_timestamp = 4000000;

    // 性能测试：添加大量订单
    let order_count = 1000;
    let start_time = Instant::now();

    for i in 0..order_count {
        let order = create_test_order(
            &format!("perf_order_{}", i),
            OrderSide::Buy,
            Some(50000.0),
            0.1,
        );
        simulator
            .add_order(order, base_timestamp + (i as u64 * 10))
            .unwrap(); // 每个订单间隔10微秒
    }

    let add_duration = start_time.elapsed();
    info!("📊 添加{}个订单耗时: {:?}", order_count, add_duration);

    // 性能测试：批量获取可执行订单
    let execution_start = Instant::now();
    let execution_timestamp = base_timestamp + 2000; // 2ms后
    let ready_orders = simulator.get_ready_orders(execution_timestamp);
    let execution_duration = execution_start.elapsed();

    info!(
        "📊 获取{}个可执行订单耗时: {:?}",
        ready_orders.len(),
        execution_duration
    );
    info!("📈 平均每个订单处理时间: {:?}", add_duration / order_count);

    // 验证性能合理性
    assert!(
        add_duration < std::time::Duration::from_millis(100),
        "添加1000个订单应该在100ms内完成"
    );
    assert!(
        execution_duration < std::time::Duration::from_millis(10),
        "获取可执行订单应该在10ms内完成"
    );

    info!("✅ 延迟模拟器性能测试完成");
}
