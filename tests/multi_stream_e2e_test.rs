use backtest::config::{DataSourceType, TimeAlignmentConfig};
use backtest::matching::{MultiQueueTimeAligner, RateLimiter};
use backtest::types::{Bbo, MarketData, Price, Trade};
use chrono::Utc;
use std::collections::HashMap;
use std::time::Duration;
use tracing::{info, warn};

/// 端到端测试：多数据源时间对齐功能
#[tokio::test]
async fn test_multi_stream_time_alignment_e2e() {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .try_init();

    info!("🚀 Starting multi-stream time alignment E2E test");

    // 1. 创建测试数据
    let test_data = create_mixed_timestamp_data();
    info!("📊 Created {} test data items", test_data.len());

    // 2. 创建时间对齐器
    let config = TimeAlignmentConfig {
        enabled: true,
        time_window_micros: 50_000,
        buffer_size_limit: 100,
        max_wait_time_micros: 1_000_000,
        time_tolerance_micros: 10_000,
    };

    let mut time_aligner = MultiQueueTimeAligner::new(config);

    // 3. 添加所有测试数据到时间对齐器
    for data in test_data {
        time_aligner.add_data(data).expect("Failed to add data");
    }

    // 4. 验证数据按时间顺序输出
    let mut output_data = Vec::new();
    while let Some(data) = time_aligner.get_next_data() {
        output_data.push((data.timestamp_for_sorting(), data.data_source_type()));
    }

    // 5. 验证时间对齐
    verify_time_alignment(&output_data);
    verify_multi_source_distribution(&output_data);

    info!("✅ Multi-stream time alignment E2E test completed successfully");
}

/// 创建混合时间戳的测试数据
fn create_mixed_timestamp_data() -> Vec<MarketData> {
    let mut data = Vec::new();

    // 创建三种数据源的数据，时间戳交错
    let sources = vec![
        DataSourceType::BinanceOfficial,
        DataSourceType::BinanceTardis,
        DataSourceType::OkxTardis,
    ];

    // 为每个数据源创建20条数据，时间戳交错
    for i in 0..20 {
        for (j, &source) in sources.iter().enumerate() {
            let base_timestamp = 1000000u64; // 1秒基准时间戳（微秒）
            let timestamp = base_timestamp + (i * 3 + j) as u64 * 1000; // 每1ms递增

            match source {
                DataSourceType::BinanceOfficial => {
                    let bbo = Bbo {
                        update_id: (i * 3 + j) as u64,
                        bid_price: Price::new(99.5 + i as f64 * 0.1),
                        bid_quantity: 10.0,
                        ask_price: Price::new(100.5 + i as f64 * 0.1),
                        ask_quantity: 15.0,
                        timestamp: Some(timestamp),
                        data_source_type: source,
                    };
                    data.push(MarketData::Bbo(bbo));
                }
                DataSourceType::BinanceTardis => {
                    // 添加BBO数据
                    let bbo = Bbo {
                        update_id: (i * 3 + j + 1000) as u64,
                        bid_price: Price::new(99.6 + i as f64 * 0.1),
                        bid_quantity: 12.0,
                        ask_price: Price::new(100.6 + i as f64 * 0.1),
                        ask_quantity: 18.0,
                        timestamp: Some(timestamp),
                        data_source_type: source,
                    };
                    data.push(MarketData::Bbo(bbo));

                    // 添加Trade数据
                    let trade = Trade {
                        id: format!("trade_{}", i * 3 + j),
                        symbol: "BTCUSDT".to_string(),
                        price: Price::new(100.0 + i as f64 * 0.1),
                        quantity: 0.5,
                        side: if i % 2 == 0 {
                            backtest::types::OrderSide::Buy
                        } else {
                            backtest::types::OrderSide::Sell
                        },
                        timestamp: Some(Utc::now()),
                        data_source_type: source,
                    };
                    data.push(MarketData::Trade(trade));
                }
                DataSourceType::OkxTardis => {
                    // 添加BBO数据
                    let bbo = Bbo {
                        update_id: (i * 3 + j + 2000) as u64,
                        bid_price: Price::new(99.7 + i as f64 * 0.1),
                        bid_quantity: 25.0,
                        ask_price: Price::new(100.7 + i as f64 * 0.1),
                        ask_quantity: 20.0,
                        timestamp: Some(timestamp),
                        data_source_type: source,
                    };
                    data.push(MarketData::Bbo(bbo));

                    // 添加Trade数据
                    let trade = Trade {
                        id: format!("okx_trade_{}", i * 3 + j),
                        symbol: "BTC-USDT".to_string(),
                        price: Price::new(100.1 + i as f64 * 0.1),
                        quantity: 0.8,
                        side: if i % 3 == 0 {
                            backtest::types::OrderSide::Buy
                        } else {
                            backtest::types::OrderSide::Sell
                        },
                        timestamp: Some(Utc::now()),
                        data_source_type: source,
                    };
                    data.push(MarketData::Trade(trade));
                }
            }
        }
    }

    // 不打乱数据顺序，因为我们要测试时间对齐功能
    // 在实际应用中，数据会通过不同的通道到达，时间对齐器会处理乱序问题
    info!(
        "📊 Created {} data items with interleaved timestamps",
        data.len()
    );
    data
}

/// 验证时间对齐
fn verify_time_alignment(output_data: &[(u64, DataSourceType)]) {
    info!(
        "🔍 Verifying time alignment for {} items...",
        output_data.len()
    );

    let mut last_timestamp = 0u64;
    let mut out_of_order_count = 0;

    for (timestamp, source_type) in output_data {
        if *timestamp < last_timestamp {
            out_of_order_count += 1;
            warn!(
                "⚠️  Out of order: {} < {} (source: {:?})",
                timestamp, last_timestamp, source_type
            );
        }
        last_timestamp = *timestamp;
    }

    let order_rate =
        (output_data.len() - out_of_order_count) as f64 / output_data.len() as f64 * 100.0;
    info!("📊 Time alignment: {:.1}% in correct order", order_rate);

    // 要求100%的数据按时间顺序（因为这是离线测试）
    assert_eq!(
        out_of_order_count,
        0,
        "Time alignment failed: {} out of {} items out of order",
        out_of_order_count,
        output_data.len()
    );

    info!("✅ Time alignment verification passed");
}

/// 验证多数据源分布
fn verify_multi_source_distribution(output_data: &[(u64, DataSourceType)]) {
    info!("🔍 Verifying multi-source data distribution...");

    let mut source_counts: HashMap<DataSourceType, usize> = HashMap::new();
    let mut source_timestamps: HashMap<DataSourceType, Vec<u64>> = HashMap::new();

    for (timestamp, source_type) in output_data {
        *source_counts.entry(*source_type).or_insert(0) += 1;
        source_timestamps
            .entry(*source_type)
            .or_insert_with(Vec::new)
            .push(*timestamp);
    }

    info!("📊 Data source distribution:");
    for (source_type, count) in &source_counts {
        info!("  {:?}: {} items", source_type, count);
    }

    // 验证至少有3种数据源
    assert!(
        source_counts.len() >= 3,
        "Expected at least 3 data sources, got: {}",
        source_counts.len()
    );

    // 验证每种数据源都有合理数量的数据
    for (source_type, count) in &source_counts {
        assert!(*count > 0, "Data source {:?} has no data", source_type);
    }

    // 验证数据源之间的时间戳交错
    verify_timestamp_interleaving(&source_timestamps);

    info!("✅ Multi-source distribution verification passed");
}

/// 验证时间戳交错
fn verify_timestamp_interleaving(source_timestamps: &HashMap<DataSourceType, Vec<u64>>) {
    info!("🔍 Verifying timestamp interleaving...");

    // 检查是否有时间戳交错（不同数据源的时间戳应该混合在一起）
    let mut all_timestamps_with_source: Vec<(u64, DataSourceType)> = Vec::new();

    for (source_type, timestamps) in source_timestamps {
        for &timestamp in timestamps {
            all_timestamps_with_source.push((timestamp, *source_type));
        }
    }

    // 按时间戳排序
    all_timestamps_with_source.sort_by_key(|(timestamp, _)| *timestamp);

    // 检查连续的数据源变化
    let mut source_changes = 0;
    let mut last_source = None;

    for (_, source_type) in &all_timestamps_with_source {
        if let Some(last) = last_source {
            if last != *source_type {
                source_changes += 1;
            }
        }
        last_source = Some(*source_type);
    }

    let change_rate = source_changes as f64 / all_timestamps_with_source.len() as f64 * 100.0;
    info!("📊 Data source changes: {:.1}% of transitions", change_rate);

    // 要求至少有一些数据源变化，证明时间戳确实交错
    assert!(
        source_changes > 0,
        "No data source changes detected - timestamps may not be properly interleaved"
    );

    info!("✅ Timestamp interleaving verification passed");
}

/// 速率限制器测试
#[tokio::test]
async fn test_rate_limiter_e2e() {
    let _ = tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .try_init();

    info!("🚀 Starting rate limiter E2E test");

    let config = backtest::config::PlaybackConfig {
        rate_per_second: 3, // 每秒3条，更容易观察限制效果
        enabled: true,
        batch_size: 1, // 单条处理，更精确
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: backtest::config::OrderLatencyConfig::default(),
    };

    let mut rate_limiter = RateLimiter::new(config);

    let start_time = std::time::Instant::now();
    let mut processed_count = 0;

    // 处理12条数据，每秒3条，应该需要大约4秒
    // 前3条立即处理，第4条开始等待1秒，第7条再等待1秒，第10条再等待1秒
    for i in 0..12 {
        // 先应用速率控制（检查是否需要等待）
        rate_limiter
            .apply_rate_control()
            .await
            .expect("Rate control failed");

        // 然后记录处理了一条数据
        rate_limiter.record_processed();
        processed_count += 1;

        let elapsed = start_time.elapsed();
        info!(
            "Processed item {}: total {} items in {:.2}s",
            i + 1,
            processed_count,
            elapsed.as_secs_f64()
        );
    }

    let total_elapsed = start_time.elapsed();
    // 分析：12条数据，每秒3条
    // 第1-3条：立即处理（0s）
    // 第4-6条：等待1s后处理（1s）
    // 第7-9条：等待1s后处理（2s）
    // 第10-12条：等待1s后处理（3s）
    // 所以总时间应该是3秒左右
    let expected_min_time = Duration::from_millis(2800); // 至少2.8秒
    let expected_max_time = Duration::from_millis(3500); // 最多3.5秒

    info!("Total time: {:.2}s", total_elapsed.as_secs_f64());

    assert!(
        total_elapsed >= expected_min_time,
        "Rate limiting too fast: {:.2}s < {:.2}s",
        total_elapsed.as_secs_f64(),
        expected_min_time.as_secs_f64()
    );

    assert!(
        total_elapsed <= expected_max_time,
        "Rate limiting too slow: {:.2}s > {:.2}s",
        total_elapsed.as_secs_f64(),
        expected_max_time.as_secs_f64()
    );

    info!("✅ Rate limiter E2E test completed successfully");
}
