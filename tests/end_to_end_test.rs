use backtest::{
    config::{Config<PERSON>anager, DataSourceType},
    framework::BacktestFramework,
    state::get_backtest_recorder,
};
use chrono::{DateTime, Utc};
use std::time::Duration;
use tokio::time::sleep;
use tracing::{info, warn};

#[tokio::test]
async fn test_end_to_end_backtest_with_auto_summary() {
    // 设置日志
    tracing_subscriber::fmt::init();

    info!("Starting end-to-end backtest test");

    // 使用默认配置
    let config = ConfigManager::get().unwrap();
    info!(
        "Using default config: websocket_port={}, http_port={}",
        config.websocket_port, config.http_port
    );

    // 创建回测框架
    let mut framework = BacktestFramework::new().await.unwrap();

    // 初始化组件
    framework.initialize_components().await.unwrap();
    framework.initialize_data_pipeline().await.unwrap();

    // 准备框架
    framework.prepare().await.unwrap();

    // 检查数据路径是否存在
    if config.data_paths.is_empty() {
        warn!("No data paths configured, skipping data streaming test");
        return;
    }

    let data_paths = &config.data_paths[0].sources;
    info!("Using data paths: {:?}", data_paths);

    // 启动数据流
    let data_stream_controller = framework.data_stream_controller().unwrap();
    let controller = data_stream_controller.lock().await;

    info!("Starting data streaming...");
    controller.start().await.unwrap();

    drop(controller);

    // 等待一段时间让数据开始流动
    info!("Waiting for data to start flowing...");
    sleep(Duration::from_secs(5)).await;

    // 检查回测记录器是否正在记录数据
    let recorder = get_backtest_recorder().await;
    if let Some(recorder) = recorder {
        let mut recorder = recorder.lock().await;
        info!("Backtest recorder status: is_recording={}, bbo_count={}, trades_count={}, orders_count={}",
              recorder.is_recording,
              recorder.bbo_records.len(),
              recorder.trades.len(),
              recorder.orders.len());

        // 手动启动回测记录
        if !recorder.is_recording {
            info!("Starting backtest recording manually");
            recorder.start_recording(Utc::now());
        }

        info!("Backtest recorder is available and working");
    } else {
        warn!("Backtest recorder not found");
    }

    // 等待数据流完成（最多等待30秒）
    info!("Waiting for data streaming to complete...");
    let max_wait_time = Duration::from_secs(30);
    let start_wait = std::time::Instant::now();

    loop {
        if start_wait.elapsed() > max_wait_time {
            warn!("Timeout waiting for data streaming completion");
            break;
        }

        // 检查数据流控制器状态
        let data_stream_controller = framework.data_stream_controller().unwrap();
        let controller = data_stream_controller.lock().await;
        let status = controller.get_status().await;

        if status == backtest::data::DataStreamStatus::Stopped {
            info!("Data streaming completed");
            break;
        }

        drop(controller);
        sleep(Duration::from_millis(500)).await;
    }

    // 验证框架组件是否正常工作
    info!("Verifying framework components...");

    // 检查HTTP服务器是否运行
    let http_client = reqwest::Client::new();
    let health_url = format!("http://localhost:{}/api/v1/health", config.http_port);
    match http_client.get(&health_url).send().await {
        Ok(response) => {
            info!(
                "HTTP server is running, health check status: {}",
                response.status()
            );
        }
        Err(e) => {
            warn!("HTTP server health check failed: {}", e);
        }
    }

    // 验证最终的回测记录器状态
    let recorder = get_backtest_recorder().await;
    if let Some(recorder) = recorder {
        let recorder = recorder.lock().await;
        info!(
            "Final backtest recorder stats: bbo_count={}, trades_count={}, orders_count={}",
            recorder.bbo_records.len(),
            recorder.trades.len(),
            recorder.orders.len()
        );

        // 验证记录器状态
        assert!(
            recorder.start_time.is_some() || recorder.end_time.is_some(),
            "Backtest recorder should have timing information"
        );
    }

    // 优雅关闭框架
    framework.shutdown().await.unwrap();

    info!("End-to-end test completed successfully");
}

#[tokio::test]
async fn test_websocket_subscription() {
    // 设置日志
    tracing_subscriber::fmt::init();

    info!("Starting WebSocket subscription test");

    // 使用默认配置
    let config = ConfigManager::get().unwrap();

    // 创建回测框架
    let mut framework = BacktestFramework::new().await.unwrap();

    // 初始化组件
    framework.initialize_components().await.unwrap();
    framework.initialize_data_pipeline().await.unwrap();

    // 准备框架
    framework.prepare().await.unwrap();

    // 启动WebSocket客户端测试
    let ws_client = tokio::spawn(async move {
        use futures_util::{SinkExt, StreamExt};
        use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

        let url = format!("ws://localhost:{}/ws", config.websocket_port);
        let (ws_stream, _) = connect_async(url).await.expect("Failed to connect");
        info!("WebSocket connected");

        let (mut write, mut read) = ws_stream.split();

        // 订阅BBO数据
        let subscribe_msg = serde_json::json!({
            "type": "subscribe",
            "channel": "bbo",
            "symbol": "BTCUSDT"
        });

        write
            .send(Message::Text(subscribe_msg.to_string()))
            .await
            .unwrap();
        info!("Sent BBO subscription");

        // 订阅trades数据
        let subscribe_trades_msg = serde_json::json!({
            "type": "subscribe",
            "channel": "trades",
            "symbol": "BTCUSDT"
        });

        write
            .send(Message::Text(subscribe_trades_msg.to_string()))
            .await
            .unwrap();
        info!("Sent trades subscription");

        // 订阅depth数据
        let subscribe_depth_msg = serde_json::json!({
            "type": "subscribe",
            "channel": "depth",
            "symbol": "BTCUSDT"
        });

        write
            .send(Message::Text(subscribe_depth_msg.to_string()))
            .await
            .unwrap();
        info!("Sent depth subscription");

        // 接收数据（最多等待10秒）
        let mut message_count = 0;
        let start_time = std::time::Instant::now();

        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    info!("Received message: {}", text);
                    message_count += 1;

                    if message_count >= 5 {
                        info!("Received enough messages, stopping");
                        break;
                    }
                }
                Ok(Message::Close(_)) => {
                    info!("WebSocket connection closed");
                    break;
                }
                Err(e) => {
                    warn!("WebSocket error: {}", e);
                    break;
                }
                _ => {}
            }

            if start_time.elapsed() > Duration::from_secs(10) {
                warn!("Timeout waiting for WebSocket messages");
                break;
            }
        }

        info!(
            "WebSocket test completed, received {} messages",
            message_count
        );
        assert!(message_count > 0, "No messages received from WebSocket");
    });

    // 等待WebSocket测试完成
    ws_client.await.unwrap();

    // 优雅关闭框架
    framework.shutdown().await.unwrap();

    info!("WebSocket subscription test completed successfully");
}
