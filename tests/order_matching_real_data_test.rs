use backtest::account::manager::Account<PERSON>anager;
use backtest::account::types::AccountConfig;
use backtest::config::{DataSourceType, OrderLatencyConfig, PlaybackConfig};
use backtest::matching::MatchingEngine;
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price};
use chrono::Utc;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{broadcast, mpsc, Mutex};
use tracing::info;

/// 创建测试用的BBO数据
fn create_test_bbo(timestamp_micros: u64, bid_price: f64, ask_price: f64) -> MarketData {
    MarketData::Bbo(Bbo {
        update_id: 0,
        bid_price: Price::new(bid_price),
        bid_quantity: 100.0,
        ask_price: Price::new(ask_price),
        ask_quantity: 100.0,
        timestamp: Some(timestamp_micros),
        data_source_type: DataSourceType::BinanceTardis,
    })
}

/// 创建测试订单
fn create_test_order(
    id: &str,
    order_type: OrderType,
    side: OrderSide,
    price: Option<f64>,
    quantity: f64,
) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type,
        side,
        price: price.map(Price::new),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
    }
}

/// 设置测试环境
async fn setup_test_environment() -> (
    MatchingEngine,
    broadcast::Sender<MarketData>,
    mpsc::Sender<Order>,
    broadcast::Receiver<backtest::types::Trade>,
    broadcast::Receiver<Order>,
) {
    // 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, trade_rx) = broadcast::channel(1000);
    let (order_update_tx, order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    // 创建账户管理器
    let mut account_config = AccountConfig::default();
    account_config.initial_balance = 1000000.0; // 100万USDT
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    // 使用默认的回放配置（启用时间对齐器）
    let playback_config = PlaybackConfig::default();

    // 创建撮合引擎
    let matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    (
        matching_engine,
        market_data_tx,
        order_tx,
        trade_rx,
        order_update_rx,
    )
}

#[tokio::test]
async fn test_market_order_with_real_data_flow() {
    tracing_subscriber::fmt::init();
    info!("🚀 开始真实数据流市价单测试");

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, mut _order_update_rx) =
        setup_test_environment().await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    tokio::time::sleep(Duration::from_millis(100)).await;

    // 模拟真实的市场数据流：发送足够多的BBO数据来触发时间对齐器
    let base_timestamp = 1000000; // 1秒基准时间戳
    let mut bbo_data = Vec::new();

    // 创建600个BBO数据点（超过默认阈值500）
    for i in 0..600 {
        let timestamp = base_timestamp + (i * 1000); // 每1ms一个数据点
        let bid_price = 50000.0 + (i as f64 * 0.1); // 价格缓慢上涨
        let ask_price = bid_price + 100.0; // ask比bid高100
        let bbo = create_test_bbo(timestamp, bid_price, ask_price);
        bbo_data.push(bbo);
    }

    info!("📊 发送{}个BBO数据点", bbo_data.len());

    // 分批发送数据，模拟真实的数据流
    for (i, bbo) in bbo_data.iter().enumerate() {
        market_data_tx.send(bbo.clone()).unwrap();

        // 每100个数据点暂停一下，让时间对齐器有机会处理
        if (i + 1) % 100 == 0 {
            info!("📈 已发送{}个数据点", i + 1);
            tokio::time::sleep(Duration::from_millis(50)).await;
        }
    }

    // 等待时间对齐器处理数据
    tokio::time::sleep(Duration::from_millis(200)).await;

    // 现在发送市价单测试
    info!("📝 测试买入市价单（应该以ask价格成交）");
    let buy_market_order =
        create_test_order("buy_market", OrderType::Market, OrderSide::Buy, None, 1.0);
    order_tx.send(buy_market_order).await.unwrap();

    // 等待交易执行
    let mut buy_trade_received = false;
    let timeout = Duration::from_millis(2000);
    let start_wait = tokio::time::Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    info!("✅ 买入市价单成交: {} @ {}", trade.id, trade.price);

                    // 验证价格是否合理（应该是ask价格，即bid+100）
                    let expected_ask_range = (50100.0, 50200.0); // 基于我们发送的数据范围
                    let actual_price = trade.price.value();

                    if actual_price >= expected_ask_range.0 && actual_price <= expected_ask_range.1 {
                        info!("✅ 买入市价单价格验证通过: {} 在预期范围 {:?}", actual_price, expected_ask_range);
                        buy_trade_received = true;
                        break;
                    } else {
                        info!("⚠️ 买入市价单价格异常: {} 不在预期范围 {:?}", actual_price, expected_ask_range);
                    }
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(10)) => {}
        }
    }

    assert!(buy_trade_received, "应该收到买入市价单的交易");

    // 测试卖出市价单
    info!("📝 测试卖出市价单（应该以bid价格成交）");
    let sell_market_order =
        create_test_order("sell_market", OrderType::Market, OrderSide::Sell, None, 1.0);
    order_tx.send(sell_market_order).await.unwrap();

    // 等待交易执行
    let mut sell_trade_received = false;
    let start_wait = tokio::time::Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    info!("✅ 卖出市价单成交: {} @ {}", trade.id, trade.price);

                    // 验证价格是否合理（应该是bid价格）
                    let expected_bid_range = (50000.0, 50100.0); // 基于我们发送的数据范围
                    let actual_price = trade.price.value();

                    if actual_price >= expected_bid_range.0 && actual_price <= expected_bid_range.1 {
                        info!("✅ 卖出市价单价格验证通过: {} 在预期范围 {:?}", actual_price, expected_bid_range);
                        sell_trade_received = true;
                        break;
                    } else {
                        info!("⚠️ 卖出市价单价格异常: {} 不在预期范围 {:?}", actual_price, expected_bid_range);
                    }
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(10)) => {}
        }
    }

    assert!(sell_trade_received, "应该收到卖出市价单的交易");

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 真实数据流市价单测试完成");
}

#[tokio::test]
async fn test_ioc_order_with_real_data() {
    info!("🚀 开始真实数据流IOC订单测试");

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, mut order_update_rx) =
        setup_test_environment().await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    tokio::time::sleep(Duration::from_millis(100)).await;

    // 发送足够的市场数据
    let base_timestamp = 1000000;
    for i in 0..600 {
        let timestamp = base_timestamp + (i * 1000);
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();

        if (i + 1) % 100 == 0 {
            tokio::time::sleep(Duration::from_millis(50)).await;
        }
    }

    tokio::time::sleep(Duration::from_millis(200)).await;

    // 测试IOC买单能立即成交的情况
    info!("📝 测试IOC买单立即成交（价格>=ask）");
    let ioc_buy_order = create_test_order(
        "ioc_buy_fill",
        OrderType::LimitIOC,
        OrderSide::Buy,
        Some(50100.0), // 价格等于ask，能立即成交
        1.0,
    );
    order_tx.send(ioc_buy_order).await.unwrap();

    // 等待交易执行
    let mut ioc_buy_filled = false;
    let timeout = Duration::from_millis(2000);
    let start_wait = tokio::time::Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    if trade.id.contains("ioc_buy_fill") {
                        info!("✅ IOC买单成交: {} @ {}", trade.id, trade.price);
                        ioc_buy_filled = true;
                        break;
                    }
                }
            }
            order_result = order_update_rx.recv() => {
                if let Ok(order_update) = order_result {
                    if order_update.id == "ioc_buy_fill" {
                        info!("📋 IOC买单状态更新: {} -> {:?}", order_update.id, order_update.status);
                        assert_eq!(order_update.status, OrderStatus::Filled, "IOC买单应该被填满");
                    }
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(10)) => {}
        }
    }

    assert!(ioc_buy_filled, "IOC买单应该立即成交");

    // 测试IOC买单不能立即成交的情况
    info!("📝 测试IOC买单过期（价格<ask）");
    let ioc_buy_expire = create_test_order(
        "ioc_buy_expire",
        OrderType::LimitIOC,
        OrderSide::Buy,
        Some(50050.0), // 价格低于ask，不能立即成交
        1.0,
    );
    order_tx.send(ioc_buy_expire).await.unwrap();

    // 等待订单过期
    let mut ioc_buy_expired = false;
    let start_wait = tokio::time::Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            order_result = order_update_rx.recv() => {
                if let Ok(order_update) = order_result {
                    if order_update.id == "ioc_buy_expire" {
                        info!("📋 IOC买单状态更新: {} -> {:?}", order_update.id, order_update.status);
                        assert_eq!(order_update.status, OrderStatus::Expired, "IOC买单应该过期");
                        ioc_buy_expired = true;
                        break;
                    }
                }
            }
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    if trade.id.contains("ioc_buy_expire") {
                        panic!("IOC买单不应该成交，但收到了交易: {} @ {}", trade.id, trade.price);
                    }
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(10)) => {}
        }
    }

    assert!(ioc_buy_expired, "IOC买单应该过期");

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 真实数据流IOC订单测试完成");
}

#[tokio::test]
async fn test_order_latency_with_real_data() {
    info!("🚀 开始真实数据流订单延迟测试");

    // 创建启用延迟的测试环境
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, mut trade_rx) = broadcast::channel(1000);
    let (order_update_tx, mut _order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    let account_config = AccountConfig::default();
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    // 启用3ms延迟，使用默认时间对齐配置
    let mut playback_config = PlaybackConfig::default();
    playback_config.order_latency = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms延迟
        max_queue_size: 1000,
        random_latency: false,
    };

    let mut matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    tokio::time::sleep(Duration::from_millis(100)).await;

    let base_timestamp = 1000000;

    // 发送足够的初始数据来触发时间对齐器
    for i in 0..600 {
        let timestamp = base_timestamp + (i * 1000);
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();

        if (i + 1) % 100 == 0 {
            tokio::time::sleep(Duration::from_millis(50)).await;
        }
    }

    tokio::time::sleep(Duration::from_millis(200)).await;

    // 提交市价单（应该延迟3ms后执行）
    let market_order = create_test_order(
        "delayed_market",
        OrderType::Market,
        OrderSide::Buy,
        None,
        1.0,
    );
    order_tx.send(market_order).await.unwrap();
    info!("📝 提交延迟市价单");

    // 继续推进市场数据时间以触发延迟订单执行
    for i in 600..610 {
        let timestamp = base_timestamp + (i * 1000);
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();
        tokio::time::sleep(Duration::from_millis(20)).await;

        // 检查是否有交易
        if let Ok(trade) = trade_rx.try_recv() {
            let delay_ms = (i - 600) + 1;
            info!(
                "✅ 延迟市价单执行: {} @ {} (延迟约{}ms)",
                trade.id, trade.price, delay_ms
            );

            // 验证市价单按设计执行（买单以ask价格成交）
            assert_eq!(trade.price.value(), 50100.0, "买入市价单应该以ask价格成交");

            break;
        }
    }

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 真实数据流订单延迟测试完成");
}
