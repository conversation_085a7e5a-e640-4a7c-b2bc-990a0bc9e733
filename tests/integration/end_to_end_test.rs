use backtest::config::{Config, DataSourceType};
use backtest::types::{Bbo, MarketData, Price, Trade};
use backtest::websocket::subscription::SubscriptionType;
use backtest::BacktestFramework;
use chrono::{DateTime, Utc};
use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use tokio::net::TcpStream;
use tokio::sync::mpsc;
use tokio::time::{sleep, timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message, MaybeTlsStream, WebSocketStream};
use tracing::{debug, info, warn};

type WsStream = WebSocketStream<MaybeTlsStream<TcpStream>>;

/// 端到端测试：多数据源时间对齐和WebSocket订阅
#[tokio::test]
async fn test_multi_stream_time_alignment_e2e() {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    info!("🚀 Starting end-to-end multi-stream time alignment test");

    // 1. 创建测试数据文件
    create_test_data_files()
        .await
        .expect("Failed to create test data files");

    // 2. 创建测试配置
    let config = create_test_config();

    // 3. 启动回测框架
    let framework = BacktestFramework::new(config.clone()).expect("Failed to create framework");

    // 在后台启动框架
    let framework_handle = tokio::spawn(async move {
        if let Err(e) = framework.run().await {
            warn!("Framework error: {}", e);
        }
    });

    // 等待服务启动
    sleep(Duration::from_secs(2)).await;

    // 4. 创建多个WebSocket客户端订阅不同的stream
    let mut clients = Vec::new();
    let subscription_types = vec![SubscriptionType::OrderBook, SubscriptionType::Trade];

    for (i, sub_type) in subscription_types.iter().enumerate() {
        let client = create_websocket_client(i, *sub_type)
            .await
            .expect("Failed to create WebSocket client");
        clients.push(client);
    }

    // 5. 收集数据并验证时间对齐
    let mut received_data: HashMap<usize, Vec<(u64, DataSourceType)>> = HashMap::new();
    let test_duration = Duration::from_secs(10);
    let start_time = std::time::Instant::now();

    info!(
        "📊 Collecting data for {} seconds...",
        test_duration.as_secs()
    );

    while start_time.elapsed() < test_duration {
        for (client_id, (ws_stream, _)) in clients.iter_mut().enumerate() {
            if let Ok(Some(msg_result)) =
                timeout(Duration::from_millis(100), ws_stream.next()).await
            {
                if let Ok(msg) = msg_result {
                    if let Ok(text) = msg.to_text() {
                        if let Some((timestamp, source_type)) = parse_message_timestamp(text) {
                            received_data
                                .entry(client_id)
                                .or_insert_with(Vec::new)
                                .push((timestamp, source_type));
                            debug!(
                                "Client {} received data: timestamp={}, source={:?}",
                                client_id, timestamp, source_type
                            );
                        }
                    }
                }
            }
        }
    }

    // 6. 验证结果
    verify_time_alignment(&received_data);
    verify_multi_source_data(&received_data);

    // 7. 清理
    for (ws_stream, _) in clients.iter_mut() {
        let _ = ws_stream.close(None).await;
    }

    framework_handle.abort();
    cleanup_test_files();

    info!("✅ End-to-end test completed successfully");
}

/// 创建测试数据文件
async fn create_test_data_files() -> Result<(), Box<dyn std::error::Error>> {
    info!("📁 Creating test data files...");

    // 确保测试数据目录存在
    fs::create_dir_all("test_data")?;

    // 创建Binance官方数据文件
    create_binance_official_data("test_data/binance_official_test.csv").await?;

    // 创建Binance Tardis数据文件
    create_binance_tardis_data("test_data/binance_tardis_test.csv").await?;

    // 创建OKX Tardis数据文件
    create_okx_tardis_data("test_data/okx_tardis_test.csv").await?;

    info!("✅ Test data files created");
    Ok(())
}

/// 创建Binance官方格式测试数据
async fn create_binance_official_data(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut lines = Vec::new();

    // CSV头部
    lines.push("update_id,bid_price,bid_qty,ask_price,ask_qty,timestamp".to_string());

    // 生成测试数据，时间戳从1000开始，每隔3000递增
    for i in 0..20 {
        let timestamp = 1000 + i * 3000; // 1000, 4000, 7000, ...
        let line = format!(
            "{},{},{},{},{},{}",
            i + 1,
            99.5 + i as f64 * 0.1,
            10.0,
            100.5 + i as f64 * 0.1,
            15.0,
            timestamp
        );
        lines.push(line);
    }

    fs::write(path, lines.join("\n"))?;
    info!(
        "📄 Created Binance official data: {} lines",
        lines.len() - 1
    );
    Ok(())
}

/// 创建Binance Tardis格式测试数据
async fn create_binance_tardis_data(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut lines = Vec::new();

    // 生成测试数据，时间戳从2000开始，每隔3000递增
    for i in 0..20 {
        let timestamp = 2000 + i * 3000; // 2000, 5000, 8000, ...

        // BBO数据
        let bbo_json = json!({
            "stream": "btcusdt@bookTicker",
            "data": {
                "u": i + 100,
                "s": "BTCUSDT",
                "b": format!("{:.1}", 99.6 + i as f64 * 0.1),
                "B": "12.0",
                "a": format!("{:.1}", 100.6 + i as f64 * 0.1),
                "A": "18.0"
            },
            "timestamp": timestamp
        });
        lines.push(bbo_json.to_string());

        // Trade数据
        let trade_json = json!({
            "stream": "btcusdt@trade",
            "data": {
                "e": "trade",
                "E": timestamp + 500,
                "s": "BTCUSDT",
                "t": i + 200,
                "p": format!("{:.1}", 100.0 + i as f64 * 0.1),
                "q": "0.5",
                "T": timestamp + 500,
                "m": false
            },
            "timestamp": timestamp + 500
        });
        lines.push(trade_json.to_string());
    }

    fs::write(path, lines.join("\n"))?;
    info!("📄 Created Binance Tardis data: {} lines", lines.len());
    Ok(())
}

/// 创建OKX Tardis格式测试数据
async fn create_okx_tardis_data(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut lines = Vec::new();

    // 生成测试数据，时间戳从3000开始，每隔3000递增
    for i in 0..20 {
        let timestamp = 3000 + i * 3000; // 3000, 6000, 9000, ...

        // Ticker数据
        let ticker_json = json!({
            "arg": {
                "channel": "tickers",
                "instId": "BTC-USDT"
            },
            "data": [{
                "instType": "SPOT",
                "instId": "BTC-USDT",
                "last": format!("{:.1}", 99.7 + i as f64 * 0.1),
                "lastSz": "0.3",
                "askPx": format!("{:.1}", 100.7 + i as f64 * 0.1),
                "askSz": "20.0",
                "bidPx": format!("{:.1}", 99.7 + i as f64 * 0.1),
                "bidSz": "25.0",
                "open24h": "99.0",
                "high24h": "101.0",
                "low24h": "98.0",
                "ts": timestamp.to_string()
            }],
            "timestamp": timestamp
        });
        lines.push(ticker_json.to_string());

        // Trade数据
        let trade_json = json!({
            "arg": {
                "channel": "trades",
                "instId": "BTC-USDT"
            },
            "data": [{
                "instId": "BTC-USDT",
                "tradeId": (i + 300).to_string(),
                "px": format!("{:.1}", 100.1 + i as f64 * 0.1),
                "sz": "0.8",
                "side": "buy",
                "ts": (timestamp + 1000).to_string()
            }],
            "timestamp": timestamp + 1000
        });
        lines.push(trade_json.to_string());
    }

    fs::write(path, lines.join("\n"))?;
    info!("📄 Created OKX Tardis data: {} lines", lines.len());
    Ok(())
}

/// 创建测试配置
fn create_test_config() -> Config {
    let mut config = Config::default();

    // WebSocket配置
    config.websocket.host = "127.0.0.1".to_string();
    config.websocket.port = 8080;

    // 数据源配置
    config.data_sources = vec![
        backtest::config::DataSourceConfig {
            name: "binance_official".to_string(),
            data_type: backtest::config::DataType::Bbo,
            data_source_type: DataSourceType::BinanceOfficial,
            file_path: "test_data/binance_official_test.csv".to_string(),
            symbol: "BTCUSDT".to_string(),
        },
        backtest::config::DataSourceConfig {
            name: "binance_tardis".to_string(),
            data_type: backtest::config::DataType::Mixed,
            data_source_type: DataSourceType::BinanceTardis,
            file_path: "test_data/binance_tardis_test.csv".to_string(),
            symbol: "BTCUSDT".to_string(),
        },
        backtest::config::DataSourceConfig {
            name: "okx_tardis".to_string(),
            data_type: backtest::config::DataType::Mixed,
            data_source_type: DataSourceType::OkxTardis,
            file_path: "test_data/okx_tardis_test.csv".to_string(),
            symbol: "BTC-USDT".to_string(),
        },
    ];

    // 回放配置 - 加快测试速度
    config.playback.rate_per_second = 50;
    config.playback.enabled = true;
    config.playback.batch_size = 5;

    // 时间对齐配置
    config.playback.time_alignment.enabled = true;
    config.playback.time_alignment.time_window_micros = 50_000; // 50ms
    config.playback.time_alignment.buffer_size_limit = 100;

    config
}

/// 创建WebSocket客户端
async fn create_websocket_client(
    client_id: usize,
    subscription_type: SubscriptionType,
) -> Result<(WsStream, SubscriptionType), Box<dyn std::error::Error>> {
    let url = "ws://127.0.0.1:8080/ws";
    let (ws_stream, _) = connect_async(url).await?;

    info!("🔌 Client {} connected to WebSocket", client_id);

    // 发送订阅消息
    let subscribe_msg = json!({
        "action": "subscribe",
        "subscription_type": match subscription_type {
            SubscriptionType::OrderBook => "orderbook",
            SubscriptionType::Trade => "trade",
        },
        "client_format": "binance"
    });

    let mut ws_stream = ws_stream;
    ws_stream
        .send(Message::Text(subscribe_msg.to_string()))
        .await?;

    info!(
        "📡 Client {} subscribed to {:?}",
        client_id, subscription_type
    );

    Ok((ws_stream, subscription_type))
}

/// 解析消息中的时间戳和数据源类型
fn parse_message_timestamp(text: &str) -> Option<(u64, DataSourceType)> {
    if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(text) {
        // 尝试解析时间戳
        let timestamp = json_value
            .get("timestamp")
            .and_then(|t| t.as_u64())
            .or_else(|| {
                json_value
                    .get("data")
                    .and_then(|d| d.get("timestamp"))
                    .and_then(|t| t.as_u64())
            })?;

        // 尝试推断数据源类型
        let source_type = if json_value.get("stream").is_some() {
            DataSourceType::BinanceTardis
        } else if json_value.get("arg").is_some() {
            DataSourceType::OkxTardis
        } else {
            DataSourceType::BinanceOfficial
        };

        Some((timestamp, source_type))
    } else {
        None
    }
}

/// 验证时间对齐
fn verify_time_alignment(received_data: &HashMap<usize, Vec<(u64, DataSourceType)>>) {
    info!("🔍 Verifying time alignment...");

    for (client_id, data) in received_data {
        info!("Client {} received {} messages", client_id, data.len());

        // 验证时间戳是否递增
        let mut last_timestamp = 0u64;
        let mut out_of_order_count = 0;

        for (timestamp, source_type) in data {
            if *timestamp < last_timestamp {
                out_of_order_count += 1;
                warn!(
                    "⚠️  Client {}: Out of order timestamp {} < {} (source: {:?})",
                    client_id, timestamp, last_timestamp, source_type
                );
            }
            last_timestamp = *timestamp;
        }

        let order_rate = (data.len() - out_of_order_count) as f64 / data.len() as f64 * 100.0;
        info!(
            "📊 Client {}: {:.1}% messages in correct time order",
            client_id, order_rate
        );

        // 要求至少95%的消息按时间顺序
        assert!(
            order_rate >= 95.0,
            "Client {} time alignment failed: only {:.1}% in order",
            client_id,
            order_rate
        );
    }

    info!("✅ Time alignment verification passed");
}

/// 验证多数据源数据
fn verify_multi_source_data(received_data: &HashMap<usize, Vec<(u64, DataSourceType)>>) {
    info!("🔍 Verifying multi-source data distribution...");

    for (client_id, data) in received_data {
        let mut source_counts: HashMap<DataSourceType, usize> = HashMap::new();

        for (_, source_type) in data {
            *source_counts.entry(*source_type).or_insert(0) += 1;
        }

        info!("📊 Client {} data sources:", client_id);
        for (source_type, count) in &source_counts {
            info!("  {:?}: {} messages", source_type, count);
        }

        // 验证至少收到了2种不同数据源的数据
        assert!(
            source_counts.len() >= 2,
            "Client {} should receive data from multiple sources, got: {:?}",
            client_id,
            source_counts.keys().collect::<Vec<_>>()
        );
    }

    info!("✅ Multi-source data verification passed");
}

/// 清理测试文件
fn cleanup_test_files() {
    let _ = fs::remove_dir_all("test_data");
    info!("🧹 Test files cleaned up");
}
