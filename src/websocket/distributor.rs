use crate::account::types::PositionSide;
use crate::config::{ConfigManager, DataSourceType};
use crate::types::{ClientFormat, MarketData, Order, SubscriptionType, Trade};
use crate::websocket::handler::{
    BinanceAccountBalance, BinanceAccountPosition, BinanceAccountUpdate, BinanceAccountUpdateData,
    BinanceBookTicker, BinanceDepthUpdate, BinanceOrderData, BinanceOrderTradeUpdate, BinanceTrade,
    OkxChannelArg, OkxDataMessage, OkxDepthResp, OkxTrade, StreamWrapper,
};
use crate::websocket::subscription::SubscriptionManager;
use crate::{BacktestError, Result};
use std::sync::Arc;
use tokio::sync::broadcast;
use tracing::{debug, error, info, warn};

/// WebSocket数据分发器
/// 负责接收撮合引擎转发的市场数据并分发给订阅的客户端
pub struct WebSocketDistributor {
    /// 市场数据接收器
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单更新接收器
    order_update_rx: Option<broadcast::Receiver<Order>>,
    trade_rx: Option<broadcast::Receiver<Trade>>,
    /// 订阅管理器
    subscription_manager: Arc<SubscriptionManager>,
}

impl WebSocketDistributor {
    /// 创建新的WebSocket数据分发器
    pub fn new(
        market_data_rx: broadcast::Receiver<MarketData>,
        subscription_manager: Arc<SubscriptionManager>,
    ) -> Self {
        Self {
            market_data_rx,
            order_update_rx: None,
            trade_rx: None,
            subscription_manager,
        }
    }

    /// 获取默认symbol（从配置中获取第一个支持的symbol）
    fn get_default_symbol(&self) -> String {
        match ConfigManager::get() {
            Ok(config) => {
                if let Some(first_symbol) = config.account.supported_symbols.first() {
                    first_symbol.clone()
                } else {
                    "BTCUSDT".to_string() // 如果配置中没有symbol，使用默认值
                }
            }
            Err(_) => "BTCUSDT".to_string(), // 如果无法获取配置，使用默认值
        }
    }
    /// 静态方法：获取默认symbol（供其他模块在无实例时使用）
    pub fn get_default_symbol_static() -> String {
        match ConfigManager::get() {
            Ok(config) => {
                if let Some(first_symbol) = config.account.supported_symbols.first() {
                    first_symbol.clone()
                } else {
                    "BTCUSDT".to_string()
                }
            }
            Err(_) => "BTCUSDT".to_string(),
        }
    }

    /// 从MarketData中提取symbol，如果没有则使用默认symbol
    fn extract_symbol_from_market_data(&self, market_data: &MarketData) -> String {
        match market_data {
            MarketData::TradeData(trade_data) => trade_data.symbol.clone(),
            _ => self.get_default_symbol(), // 对于没有symbol字段的数据类型，使用默认symbol
        }
    }

    /// 设置订单更新接收器
    pub fn set_order_update_receiver(&mut self, order_update_rx: broadcast::Receiver<Order>) {
        self.order_update_rx = Some(order_update_rx);
    }

    pub fn set_trade_receiver(&mut self, trade_rx: broadcast::Receiver<Trade>) {
        self.trade_rx = Some(trade_rx);
    }

    /// 启动数据分发
    pub async fn start_distribution(&mut self) -> Result<()> {
        info!("Starting WebSocket data distribution");

        loop {
            tokio::select! {
                // 处理市场数据
                market_data_result = self.market_data_rx.recv() => {
                    match market_data_result {
                        Ok(market_data) => {
                            debug!("📡 WebSocket distributor received market data: {:?}", market_data);
                            if let Err(e) = self.distribute_market_data(market_data).await {
                                error!("Failed to distribute market data: {}", e);
                                continue;
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            debug!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }
                order_update_result = async {
                    if let Some(ref mut order_rx) = self.order_update_rx {
                        order_rx.recv().await
                    } else {
                        // 如果没有订单更新接收器，永远等待
                        std::future::pending().await
                    }
                } => {
                    debug!("Received order update {:?}", order_update_result);
                    match order_update_result {
                        Ok(order) => {
                            if let Err(e) = self.distribute_order_update(order).await {
                                error!("Failed to distribute order update: {}", e);
                                continue;
                            }
                            // 在订单更新后，推送一次账户余额与持仓更新（ACCOUNT_UPDATE）
                            if let Err(e) = self.distribute_position_update().await {
                                error!("Failed to distribute account/position update: {}", e);
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Order update channel closed");
                            // 不退出，继续处理市场数据
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            debug!("Order updates lagged, skipped {} messages", skipped);
                        }
                    }
                }
            }
        }

        info!("WebSocket data distribution stopped");
        Ok(())
    }

    /// 分发市场数据给订阅的客户端
    async fn distribute_market_data(&self, market_data: MarketData) -> Result<()> {
        let subscription_type = self.get_subscription_type(&market_data);

        // 检查是否有客户端订阅了这种数据类型
        let subscriber_count = self
            .subscription_manager
            .subscription_count(&subscription_type);
        if subscriber_count == 0 {
            debug!(
                "No subscribers for {:?}, skipping distribution",
                subscription_type
            );
            return Ok(());
        }

        // 根据客户端格式分别发送数据
        self.broadcast_to_clients_by_format(&market_data, &subscription_type)
            .await?;

        debug!(
            "Distributed {:?} data to {} subscribers",
            subscription_type, subscriber_count
        );

        Ok(())
    }

    /// 根据客户端格式分别广播数据
    async fn broadcast_to_clients_by_format(
        &self,
        market_data: &MarketData,
        subscription_type: &SubscriptionType,
    ) -> Result<()> {
        // 获取所有订阅此类型数据的客户端
        let clients = self
            .subscription_manager
            .get_subscriber_details(subscription_type);

        for client in clients {
            match (&client.format, market_data.data_source_type()) {
                (ClientFormat::Binance, DataSourceType::BinanceTardis) => {
                    match &market_data {
                        MarketData::TradeData(trade_data) => {
                            tracing::info!(
                                "distrubting trade: {:?}",
                                trade_data.timestamp_datetime()
                            )
                        }
                        MarketData::OrderBook(_book_data) => {}
                        MarketData::Bbo(bbo_data) => {
                            tracing::info!("distrubting bbo: {:?}", bbo_data.timestamp_datetime())
                        }
                    }
                    if let Some(binance_data) = self.convert_to_binance_format(market_data) {
                        let stream_name = self.get_stream_name(market_data);
                        let stream_data = StreamWrapper {
                            stream: stream_name,
                            data: binance_data,
                        };
                        let msg = serde_json::to_string(&stream_data).map_err(|e| {
                            BacktestError::WebSocket(format!(
                                "Failed to serialize Binance data: {}",
                                e
                            ))
                        })?;
                        debug!(
                            "📤 Sending Binance data to client {}: {:?} from {:?}",
                            client.client_id,
                            client.format,
                            market_data.data_source_type()
                        );
                        if let Err(e) = client.sender.send(msg).await {
                            tracing::warn!(
                                "Failed to send message to client {}: {}",
                                client.client_id,
                                e
                            );
                        }
                    } else {
                        warn!(
                            "Failed to convert market data to Binance format: {:?}",
                            market_data
                        );
                        continue; // 跳过无法转换的数据
                    }
                }
                (ClientFormat::Okx, DataSourceType::OkxTardis) => {
                    // 转换为OKX格式（支持所有数据源）
                    if let Some(okx_data) = self.convert_to_okx_format(market_data) {
                        let message = serde_json::to_string(&okx_data).map_err(|e| {
                            BacktestError::WebSocket(format!("Failed to serialize OKX data: {}", e))
                        })?;
                        debug!(
                            "📤 Sending OKX data to client {}: {:?} from {:?}",
                            client.client_id,
                            client.format,
                            market_data.data_source_type()
                        );
                        if let Err(e) = client.sender.send(message).await {
                            tracing::warn!(
                                "Failed to send message to client {}: {}",
                                client.client_id,
                                e
                            );
                        };
                    } else {
                        warn!(
                            "Failed to convert market data to OKX format: {:?}",
                            market_data
                        );
                        continue; // 跳过无法转换的数据
                    }
                }
                (_, _) => {}
            };
        }

        Ok(())
    }

    /// 分发订单更新给相关客户端
    async fn distribute_order_update(&self, order: Order) -> Result<()> {
        debug!(
            "Distributing order update: {} status={:?}",
            order.id, order.status
        );

        // 生成数字订单ID（从字符串ID中提取或生成）
        let order_id_num = order
            .id
            .chars()
            .filter(|c| c.is_ascii_digit())
            .collect::<String>()
            .parse::<u64>()
            .unwrap_or_else(|_| {
                // 如果无法解析，使用时间戳作为ID
                chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0) as u64
            });

        // 创建Binance格式的ORDER_TRADE_UPDATE事件
        let order_update = BinanceOrderTradeUpdate {
            event_type: "ORDER_TRADE_UPDATE".to_string(),
            event_time: order.timestamp.timestamp_millis() as u64,
            transaction_time: order.timestamp.timestamp_millis() as u64,
            order: BinanceOrderData {
                symbol: order.symbol.clone(),
                client_order_id: order.client_order_id.clone(),
                side: match order.side {
                    crate::types::OrderSide::Buy => "BUY".to_string(),
                    crate::types::OrderSide::Sell => "SELL".to_string(),
                },
                order_type: match order.order_type {
                    crate::types::OrderType::Market => "MARKET".to_string(),
                    crate::types::OrderType::Limit => "LIMIT".to_string(),
                    crate::types::OrderType::LimitIOC => "LIMIT".to_string(),
                    crate::types::OrderType::LimitGTX => "LIMIT".to_string(),
                },
                time_in_force: match order.order_type {
                    crate::types::OrderType::LimitIOC => "IOC".to_string(),
                    crate::types::OrderType::LimitGTX => "GTX".to_string(),
                    _ => "GTC".to_string(),
                },
                original_quantity: format!("{:.8}", order.quantity),
                original_price: order
                    .price
                    .map(|p| format!("{:.8}", p.value()))
                    .unwrap_or("0".to_string()),
                average_price: order
                    .execution_info
                    .as_ref()
                    .and_then(|info| info.average_price)
                    .map(|p| format!("{:.8}", p.value()))
                    .unwrap_or("0.0".to_string()),
                stop_price: "0".to_string(),
                execution_type: match order.status {
                    crate::types::OrderStatus::Pending => "NEW".to_string(),
                    crate::types::OrderStatus::PartiallyFilled => "TRADE".to_string(),
                    crate::types::OrderStatus::Filled => "TRADE".to_string(),
                    crate::types::OrderStatus::Cancelled => "CANCELED".to_string(),
                    crate::types::OrderStatus::Expired => "EXPIRED".to_string(),
                    crate::types::OrderStatus::Rejected => "REJECTED".to_string(),
                },
                order_status: match order.status {
                    crate::types::OrderStatus::Pending => "NEW".to_string(),
                    crate::types::OrderStatus::PartiallyFilled => "PARTIALLY_FILLED".to_string(),
                    crate::types::OrderStatus::Filled => "FILLED".to_string(),
                    crate::types::OrderStatus::Cancelled => "CANCELED".to_string(),
                    crate::types::OrderStatus::Expired => "EXPIRED".to_string(),
                    crate::types::OrderStatus::Rejected => "REJECTED".to_string(),
                },
                order_id: order_id_num,
                last_filled_quantity: order
                    .execution_info
                    .as_ref()
                    .map(|info| format!("{:.8}", info.last_filled_quantity))
                    .unwrap_or("0".to_string()),
                filled_quantity: order
                    .execution_info
                    .as_ref()
                    .map(|info| format!("{:.8}", info.filled_quantity))
                    .unwrap_or("0".to_string()),
                last_filled_price: order
                    .execution_info
                    .as_ref()
                    .and_then(|info| info.last_filled_price)
                    .map(|p| format!("{:.8}", p.value()))
                    .unwrap_or("0".to_string()),
                commission_asset: order
                    .execution_info
                    .as_ref()
                    .map(|info| info.commission_asset.clone())
                    .unwrap_or("USDT".to_string()),
                commission: order
                    .execution_info
                    .as_ref()
                    .map(|info| format!("{:.8}", info.commission))
                    .unwrap_or("0".to_string()),
                order_trade_time: order.timestamp.timestamp_millis() as u64,
                trade_id: order
                    .execution_info
                    .as_ref()
                    .and_then(|info| info.trade_id.as_ref())
                    .and_then(|id| {
                        id.chars()
                            .filter(|c| c.is_ascii_digit())
                            .collect::<String>()
                            .parse::<u64>()
                            .ok()
                    })
                    .unwrap_or(0),
                bids_notional: "0".to_string(),
                ask_notional: "0".to_string(),
                is_maker: false,
                reduce_only: order.reduce_only,
                working_type: "CONTRACT_PRICE".to_string(),
                original_order_type: match order.order_type {
                    crate::types::OrderType::Market => "MARKET".to_string(),
                    crate::types::OrderType::Limit => "LIMIT".to_string(),
                    crate::types::OrderType::LimitIOC => "LIMIT_IOC".to_string(),
                    crate::types::OrderType::LimitGTX => "LIMIT_GTX".to_string(),
                },
                position_side: "BOTH".to_string(),
                close_position: false,
                activation_price: "0".to_string(),
                callback_rate: "0".to_string(),
                price_protect: false,
                ignore_si: 0,
                ignore_ss: 0,
                realized_profit: "0".to_string(),
                stp_mode: "NONE".to_string(),
                price_match_mode: "NONE".to_string(),
                gtd_auto_cancel_time: 0,
            },
        };

        // 序列化为JSON
        let message_json = serde_json::to_string(&order_update).map_err(|e| {
            BacktestError::WebSocket(format!("Failed to serialize order update: {}", e))
        })?;

        let stream_data = StreamWrapper {
            stream: "test_listen_key".to_string(),
            data: serde_json::from_str(&message_json).unwrap(),
        };

        // 直接发送给所有连接的客户端（订单更新不需要特定订阅）
        // 在实际实现中，应该只发送给下单的客户端
        self.subscription_manager
            .broadcast(
                &SubscriptionType::OrderAndFill,
                serde_json::to_string(&stream_data).unwrap(),
            )
            .await;
        debug!("Order update distributed: {}", order.id);

        Ok(())
    }

    /// 分发账户余额与持仓更新（Binance ACCOUNT_UPDATE 事件）
    pub async fn distribute_position_update(&self) -> Result<()> {
        // 获取账户管理器
        let Some(account_manager) = crate::state::get_account_manager().await else {
            warn!("Account manager not available; skip ACCOUNT_UPDATE distribution");
            return Ok(());
        };
        let manager = account_manager.lock().await;
        let summary = manager.get_account_summary();
        drop(manager);

        // 读取保证金模式
        let margin_mode = match ConfigManager::get() {
            Ok(cfg) => match cfg.account.margin_mode {
                crate::account::types::MarginMode::Cross => "cross".to_string(),
                crate::account::types::MarginMode::Isolated => "isolated".to_string(),
            },
            Err(_) => "cross".to_string(),
        };

        // 构造余额列表（仅包含非零资产）
        let balances: Vec<BinanceAccountBalance> = summary
            .balances
            .iter()
            .map(|(asset, bal)| BinanceAccountBalance {
                asset: asset.clone(),
                wallet_balance: format!("{:.8}", bal.total),
                cross_wallet_balance: format!("{:.8}", bal.available),
                balance_change: "0".to_string(),
            })
            .collect();

        // 构造持仓列表
        let positions: Vec<BinanceAccountPosition> = summary
            .positions
            .iter()
            .map(|p| {
                let ps = match p.side {
                    PositionSide::Long => "LONG",
                    PositionSide::Short => "SHORT",
                    PositionSide::None => "BOTH",
                }
                .to_string();
                BinanceAccountPosition {
                    symbol: p.symbol.clone(),
                    position_amount: format!("{:.8}", p.quantity),
                    entry_price: format!("{:.8}", p.avg_price.value()),
                    accumulated_realized: format!("{:.8}", p.realized_pnl),
                    unrealized_pnl: format!("{:.8}", p.unrealized_pnl),
                    margin_type: margin_mode.clone(),
                    isolated_wallet: if margin_mode == "isolated" {
                        format!("{:.8}", p.margin)
                    } else {
                        "0".to_string()
                    },
                    position_side: ps,
                }
            })
            .collect();

        // 组装ACCOUNT_UPDATE事件
        let account_update = BinanceAccountUpdate {
            event_type: "ACCOUNT_UPDATE".to_string(),
            event_time: chrono::Utc::now().timestamp_millis() as u64,
            transaction_time: chrono::Utc::now().timestamp_millis() as u64,
            account: BinanceAccountUpdateData {
                event_reason_type: "ORDER".to_string(), // 由订单或成交触发
                balances,
                positions,
            },
        };

        // 包装为Binance组合流（listen key）
        let stream_data = StreamWrapper {
            stream: "test_listen_key".to_string(),
            data: serde_json::to_value(&account_update).map_err(|e| {
                BacktestError::WebSocket(format!("Serialize ACCOUNT_UPDATE failed: {}", e))
            })?,
        };

        // 广播到OrderAndFill订阅
        self.subscription_manager
            .broadcast(
                &SubscriptionType::OrderAndFill,
                serde_json::to_string(&stream_data).unwrap(),
            )
            .await;

        debug!(
            "ACCOUNT_UPDATE distributed: balances={} positions={}",
            stream_data
                .data
                .get("a")
                .and_then(|a| a.get("B"))
                .and_then(|b| b.as_array())
                .map(|v| v.len())
                .unwrap_or(0),
            stream_data
                .data
                .get("a")
                .and_then(|a| a.get("P"))
                .and_then(|p| p.as_array())
                .map(|v| v.len())
                .unwrap_or(0)
        );

        Ok(())
    }

    /// 根据市场数据类型获取对应的订阅类型
    fn get_subscription_type(&self, market_data: &MarketData) -> SubscriptionType {
        match market_data {
            MarketData::OrderBook(_) => SubscriptionType::OrderBook,
            MarketData::Bbo(_) => SubscriptionType::BookTicker, // Bbo映射到BookTicker订阅类型
            MarketData::TradeData(_) => SubscriptionType::Trade, // TradeData映射到AggTrade订阅类型
        }
    }

    /// 根据市场数据类型生成对应的流名称
    fn get_stream_name(&self, market_data: &MarketData) -> String {
        let symbol = self.extract_symbol_from_market_data(market_data);
        let symbol_lower = symbol.to_lowercase();

        match market_data {
            MarketData::Bbo(_) => {
                format!("{}@bookTicker", symbol_lower)
            }
            MarketData::TradeData(_) => {
                format!("{}@trade", symbol_lower)
            }
            MarketData::OrderBook(_) => {
                format!("{}@depth20@100ms", symbol_lower)
            }
        }
    }

    /// 将内部MarketData转换为Binance格式
    fn convert_to_binance_format(&self, market_data: &MarketData) -> Option<serde_json::Value> {
        match market_data {
            MarketData::OrderBook(orderbook) => {
                let mut bids = Vec::new();
                let mut asks = Vec::new();

                for (price, qty) in orderbook.bids.iter().take(20) {
                    bids.push([format!("{:.8}", price.value()), format!("{:.8}", qty)]);
                }

                for (price, qty) in orderbook.asks.iter().take(20) {
                    asks.push([format!("{:.8}", price.value()), format!("{:.8}", qty)]);
                }

                let binance_depth = BinanceDepthUpdate {
                    event_type: "depthUpdate".to_string(),
                    event_time: (orderbook.timestamp / 1000) as u64, // 转换微秒到毫秒
                    transaction_time: (orderbook.timestamp / 1000) as u64, // 转换微秒到毫秒
                    symbol: "BTCUSDT".to_string(),
                    first_update_id: orderbook.update_id,
                    final_update_id: orderbook.update_id,
                    prev_final_update_id: orderbook.update_id.saturating_sub(1),
                    bids,
                    asks,
                };
                debug!("binance_depth: {:?}", binance_depth);
                serde_json::to_value(binance_depth).ok()
            }
            MarketData::TradeData(trade_data) => {
                // 转换TradeData为Binance AggTrade格式
                let binance_agg_trade = BinanceTrade {
                    event_type: "trade".to_string(),
                    event_time: (trade_data.timestamp / 1000) as u64, // 转换微秒到毫秒
                    symbol: trade_data.symbol.to_uppercase(),
                    trade_id: trade_data.id.parse().unwrap_or(0),
                    price: format!("{:.8}", trade_data.price.value()),
                    quantity: format!("{:.8}", trade_data.amount),
                    trade_time: (trade_data.timestamp / 1000) as u64,
                    is_buyer_maker: match trade_data.side {
                        crate::types::OrderSide::Sell => true, // 卖单是maker
                        crate::types::OrderSide::Buy => false, // 买单是taker
                    },
                };
                serde_json::to_value(binance_agg_trade).ok()
            }
            MarketData::Bbo(bbo) => {
                // 转换Bbo为Binance BookTicker格式
                let symbol = self.extract_symbol_from_market_data(market_data);
                let binance_book_ticker = BinanceBookTicker {
                    event_type: "bookTicker".to_string(),
                    event_time: bbo
                        .timestamp
                        .unwrap_or(chrono::Utc::now().timestamp_millis() as u64)
                        as u64,
                    transaction_time: chrono::Utc::now().timestamp_millis() as u64,
                    symbol,
                    update_id: bbo.update_id,
                    best_bid_price: format!("{:.8}", bbo.bid_price.value()),
                    best_bid_qty: format!("{:.8}", bbo.bid_quantity),
                    best_ask_price: format!("{:.8}", bbo.ask_price.value()),
                    best_ask_qty: format!("{:.8}", bbo.ask_quantity),
                };
                serde_json::to_value(binance_book_ticker).ok()
            }
        }
    }

    /// 将内部MarketData转换为OKX格式
    fn convert_to_okx_format(&self, market_data: &MarketData) -> Option<serde_json::Value> {
        match market_data {
            MarketData::TradeData(trade_data) => {
                let okx_trade = OkxTrade {
                    inst_id: "BTC-USDT".to_string(),
                    trade_id: trade_data.id.clone(),
                    ts: (trade_data.timestamp / 1000).to_string(),
                    px: trade_data.price.value().to_string(),
                    sz: trade_data.amount.to_string(),
                    side: match trade_data.side {
                        crate::types::OrderSide::Buy => "buy".to_string(),
                        crate::types::OrderSide::Sell => "sell".to_string(),
                    },
                };

                // 包装为OKX数据消息格式
                let data_msg = OkxDataMessage {
                    arg: OkxChannelArg {
                        channel: "trades".to_string(),
                        inst_id: "BTC-USDT".to_string(),
                    },
                    data: vec![serde_json::to_value(okx_trade).ok()?],
                };
                serde_json::to_value(data_msg).ok()
            }
            MarketData::Bbo(bbo) => {
                let okx_ticker = OkxDepthResp {
                    symbol: "BTC-USDT".to_string(),
                    bids: vec![(
                        bbo.bid_price.value().to_string(),
                        bbo.bid_quantity.to_string(),
                    )],
                    asks: vec![(
                        bbo.ask_price.value().to_string(),
                        bbo.ask_quantity.to_string(),
                    )],
                    ts: (bbo.timestamp.unwrap_or(0) as i64).to_string(),
                };
                let data_msg = OkxDataMessage {
                    arg: OkxChannelArg {
                        channel: "bbo-tbt".to_string(),
                        inst_id: "BTC-USDT".to_string(),
                    },
                    data: vec![serde_json::to_value(okx_ticker).ok()?],
                };
                serde_json::to_value(data_msg).ok()
            }
            _ => None, // 其他类型暂时不支持OKX格式
        }
    }
}
