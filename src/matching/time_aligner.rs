use crate::config::{DataSourceType, TimeAlignmentConfig};
use crate::types::MarketData;
use crate::Result;
use std::collections::{HashMap, VecDeque};
use tracing::{debug, warn};

/// 时间同步的市场数据项
#[derive(Debug, Clone)]
struct TimestampedMarketData {
    data: MarketData,
    timestamp: u64,
}

/// 多队列时间对齐器
/// 为每种数据源类型维护独立的队列，确保数据按时间戳顺序处理
#[derive(Debug)]
pub struct MultiQueueTimeAligner {
    /// 按数据源类型分组的数据队列
    queues: HashMap<DataSourceType, VecDeque<TimestampedMarketData>>,
    /// 时间对齐配置
    config: TimeAlignmentConfig,
}

impl MultiQueueTimeAligner {
    /// 创建新的多队列时间对齐器
    pub fn new(config: TimeAlignmentConfig) -> Self {
        Self {
            queues: HashMap::new(),
            config,
        }
    }

    /// 添加数据到对应的队列
    pub fn add_data(&mut self, data: MarketData) -> Result<()> {
        let timestamp = data.timestamp_for_sorting();
        let data_source_type = data.data_source_type();

        let timestamped_data = TimestampedMarketData { data, timestamp };

        // 获取或创建对应数据源的队列
        let queue = self
            .queues
            .entry(data_source_type)
            .or_insert_with(VecDeque::new);

        // 检查队列大小限制
        if queue.len() >= self.config.buffer_size_limit {
            warn!(
                "Queue for {:?} reached size limit, removing oldest data",
                data_source_type
            );
            // queue.pop_front();
        }

        // 将数据插入到正确的位置以保持时间戳顺序
        // 由于大多数情况下数据是按时间顺序到达的，我们从后往前查找插入位置
        let mut insert_pos = queue.len();
        for (i, existing_data) in queue.iter().enumerate().rev() {
            if timestamped_data.timestamp >= existing_data.timestamp {
                insert_pos = i + 1;
                break;
            }
        }

        queue.insert(insert_pos, timestamped_data);
        debug!(
            "Added data to {:?} queue at position {}, queue size: {}",
            data_source_type,
            insert_pos,
            queue.len()
        );

        Ok(())
    }

    /// 获取所有队列中时间戳最小的数据
    pub fn get_next_data(&mut self) -> Option<MarketData> {
        let mut min_timestamp = u64::MAX;
        let mut selected_source: Option<DataSourceType> = None;

        // 找到所有队列中时间戳最小的数据
        for (source_type, queue) in &self.queues {
            if let Some(front_data) = queue.front() {
                if front_data.timestamp < min_timestamp {
                    min_timestamp = front_data.timestamp;
                    selected_source = Some(*source_type);
                }
            }
        }

        // 从选中的队列中取出数据
        if let Some(source_type) = selected_source {
            if let Some(queue) = self.queues.get_mut(&source_type) {
                if let Some(timestamped_data) = queue.pop_front() {
                    debug!(
                        "Retrieved data from {:?} queue with timestamp {}, remaining: {}",
                        source_type,
                        timestamped_data.timestamp,
                        queue.len()
                    );
                    return Some(timestamped_data.data);
                }
            }
        }

        None
    }

    /// 批量获取数据，保持时间顺序
    pub fn get_batch_data(&mut self, batch_size: usize) -> Vec<MarketData> {
        let mut result = Vec::with_capacity(batch_size);

        for _ in 0..batch_size {
            if let Some(data) = self.get_next_data() {
                result.push(data);
            } else {
                break;
            }
        }

        result
    }

    /// 获取所有队列的总数据量
    pub fn total_size(&self) -> usize {
        self.queues.values().map(|queue| queue.len()).sum()
    }

    /// 检查是否有数据可以处理
    pub fn has_data(&self) -> bool {
        self.queues.values().any(|queue| !queue.is_empty())
    }

    /// 检查是否应该处理数据（基于配置的阈值）
    pub fn should_process_data(&self) -> bool {
        if !self.config.enabled {
            return self.has_data();
        }

        let total_size = self.total_size();
        let threshold = self.config.buffer_size_limit / 2; // 使用一半作为处理阈值

        total_size >= threshold
    }

    /// 清空所有队列
    pub fn clear(&mut self) {
        for queue in self.queues.values_mut() {
            queue.clear();
        }
    }

    /// 获取各个队列的状态信息
    pub fn get_queue_stats(&self) -> HashMap<DataSourceType, usize> {
        self.queues
            .iter()
            .map(|(source_type, queue)| (*source_type, queue.len()))
            .collect()
    }

    /// 更新配置
    pub fn update_config(&mut self, config: TimeAlignmentConfig) {
        self.config = config;
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &TimeAlignmentConfig {
        &self.config
    }
}
