use crate::account::AccountManager;
use crate::config::PlaybackConfig;
use crate::matching::{OrderBook, OrderLatencySimulator};
use crate::types::{
    Bbo, CancelOrderRequest, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade,
};
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, Mutex};
use tracing::{debug, error, info, warn};

pub struct OrderManager {
    pub(crate) orderbook: Arc<Mutex<OrderBook>>,
    pub(crate) account_manager: Arc<Mutex<AccountManager>>,
    pub(crate) trade_tx: broadcast::Sender<Trade>,
    pub(crate) order_update_tx: broadcast::Sender<Order>,

    // Order state
    pending_orders: HashMap<String, Order>,
    frozen_margins: HashMap<String, f64>,

    // Market state caches for matching
    current_prices: HashMap<String, f64>,
    current_bbo: HashMap<String, Bbo>,

    // Latency and time
    order_latency_simulator: OrderLatencySimulator,
    current_market_timestamp: u64,
}

impl OrderManager {
    pub fn new(
        orderbook: Arc<Mutex<OrderBook>>,
        account_manager: Arc<Mutex<AccountManager>>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        playback_config: PlaybackConfig,
    ) -> Self {
        let order_latency_simulator =
            OrderLatencySimulator::new(playback_config.order_latency.clone());
        Self {
            orderbook,
            account_manager,
            trade_tx,
            order_update_tx,
            pending_orders: HashMap::new(),
            frozen_margins: HashMap::new(),
            current_prices: HashMap::new(),
            current_bbo: HashMap::new(),
            order_latency_simulator,
            current_market_timestamp: 0,
        }
    }

    pub fn update_latency_config(&mut self, config: crate::config::OrderLatencyConfig) {
        self.order_latency_simulator.update_config(config);
    }

    pub fn get_order_latency_stats(&self) -> crate::matching::OrderLatencyStats {
        self.order_latency_simulator.get_stats()
    }

    pub fn get_current_market_timestamp(&self) -> u64 {
        self.current_market_timestamp
    }
    pub fn set_current_market_timestamp(&mut self, ts: u64) {
        self.current_market_timestamp = ts;
    }
    pub fn clear_delayed_orders(&mut self) {
        self.order_latency_simulator.clear();
    }

    fn normalize_symbol(sym: &str) -> String {
        sym.trim().to_uppercase().replace('-', "").replace('/', "")
    }

    pub async fn update_bbo_cache_from_market_data(&mut self, market_data: &MarketData) {
        match market_data {
            MarketData::Bbo(bbo) => {
                self.current_bbo.insert(bbo.symbol.clone(), bbo.clone());
            }
            _ => {}
        }
    }

    pub async fn update_account_prices(&mut self, market_data: &MarketData) {
        match market_data {
            MarketData::Bbo(bbo) => {
                let mid = (bbo.bid_price.value() + bbo.ask_price.value()) / 2.0;
                let sym = Self::normalize_symbol(&bbo.symbol);
                self.update_current_price(sym.clone(), mid);
                let mut am = self.account_manager.lock().await;
                am.update_price(sym, Price::new(mid), bbo.timestamp_datetime().unwrap());
            }
            MarketData::TradeData(td) => {
                // 统一symbol格式，避免大小写或分隔符不一致导致账户价格缓存与仓位键不匹配
                let sym = Self::normalize_symbol(&td.symbol);
                self.update_current_price(sym.clone(), td.price.value());
                let mut am = self.account_manager.lock().await;
                am.update_price(sym, td.price, td.timestamp_datetime());
            }
            _ => {}
        }
    }

    pub fn get_current_bbo(&self, symbol: &str) -> Option<&Bbo> {
        self.current_bbo.get(symbol)
    }
    pub fn get_current_market_price(&self, symbol: &str) -> Option<f64> {
        self.current_prices.get(symbol).copied()
    }
    pub fn update_current_price(&mut self, symbol: String, price: f64) {
        self.current_prices.insert(symbol, price);
    }

    /// 测试辅助：是否存在待处理订单
    pub fn has_pending_order(&self, order_id: &str) -> bool {
        self.pending_orders.contains_key(order_id)
    }

    /// 测试辅助：获取冻结保证金
    pub fn get_frozen_margin(&self, order_id: &str) -> Option<f64> {
        self.frozen_margins.get(order_id).copied()
    }

    pub async fn record_order(&self, order: &Order) {
        use crate::state::get_backtest_recorder;
        if let Some(recorder) = get_backtest_recorder().await {
            let mut recorder = recorder.lock().await;
            let mut o = order.clone();
            if self.current_market_timestamp > 0 {
                match o.status {
                    OrderStatus::Cancelled | OrderStatus::Rejected => {}
                    _ => {
                        o.timestamp =
                            DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                                .unwrap_or_else(|| order.timestamp);
                    }
                }
            }
            recorder.record_order(&o);
        }
    }

    pub async fn send_order_update(&self, mut order: Order) -> Result<()> {
        if self.current_market_timestamp > 0 {
            match order.status {
                OrderStatus::Cancelled | OrderStatus::Rejected => {}
                _ => {
                    order.timestamp =
                        DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                            .unwrap_or_else(|| Utc::now());
                }
            }
        }
        self.record_order(&order).await;
        if let Err(e) = self.order_update_tx.send(order) {
            return Err(BacktestError::Communication(format!(
                "Failed to send order update: {}",
                e
            )));
        }
        Ok(())
    }

    pub async fn validate_and_freeze_order_margin(&mut self, order: &Order) -> Result<()> {
        let mut am = self.account_manager.lock().await;
        let required = self.calculate_order_margin(order, &am).await?;
        let available = am.get_balance("USDT");
        if available < required {
            return Err(BacktestError::Account(format!(
                "Insufficient margin: required {}, available {}",
                required, available
            )));
        }
        am.freeze_margin("USDT", required)
            .map_err(|e| BacktestError::Account(format!("Failed to freeze margin: {}", e)))?;
        self.frozen_margins.insert(order.id.clone(), required);
        Ok(())
    }

    pub async fn calculate_order_margin(&self, order: &Order, am: &AccountManager) -> Result<f64> {
        let order_price = match order.order_type {
            OrderType::Market => Price::new(
                self.get_current_market_price(&order.symbol)
                    .ok_or_else(|| {
                        BacktestError::Matching(
                            "No market price available for market order".to_string(),
                        )
                    })?,
            ),
            _ => order.price.ok_or_else(|| {
                BacktestError::Matching("Limit order must have price".to_string())
            })?,
        };
        // 计算用于手续费与保证金的有效数量（reduce-only情况下不能超过当前可平仓数量）
        let sym = Self::normalize_symbol(&order.symbol);
        let effective_qty = if order.reduce_only {
            let pos_qty = am.get_position(&sym).map(|p| p.quantity).unwrap_or(0.0);
            match order.side {
                OrderSide::Buy => {
                    if pos_qty < 0.0 {
                        (-pos_qty).min(order.quantity)
                    } else {
                        0.0
                    }
                }
                OrderSide::Sell => {
                    if pos_qty > 0.0 {
                        (pos_qty).min(order.quantity)
                    } else {
                        0.0
                    }
                }
            }
        } else {
            order.quantity
        };
        let notional = order_price.value() * effective_qty;
        let max_leverage = am.get_max_leverage();
        let required = if order.reduce_only {
            0.0
        } else {
            (order_price.value() * order.quantity) / max_leverage
        };
        let fee_rate = match order.order_type {
            OrderType::Market => am.get_taker_fee_rate(),
            _ => am.get_maker_fee_rate(),
        };
        // 手续费返佣不应降低所需保证金，故仅在正手续费时提高保证金需求
        let estimated_fee = (notional * fee_rate).max(0.0);
        Ok(required + estimated_fee)
    }

    pub async fn release_order_margin(&mut self, order_id: &str) -> Result<()> {
        if let Some(frozen) = self.frozen_margins.remove(order_id) {
            let mut am = self.account_manager.lock().await;
            if let Err(e) = am.unfreeze_margin("USDT", frozen) {
                self.frozen_margins.insert(order_id.to_string(), frozen);
                return Err(BacktestError::Account(format!(
                    "Failed to release margin: {}",
                    e
                )));
            }
        }
        Ok(())
    }

    pub async fn process_order(&mut self, mut order: Order) -> Result<()> {
        if self.current_market_timestamp > 0 {
            order.timestamp = DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                .unwrap_or_else(|| Utc::now());
        }
        self.record_order(&order).await;
        info!(
            "Processing order: {:?} at {}",
            order.client_order_id, order.timestamp
        );
        // reduce-only 预检查：若订单为reduce_only，且方向会增加敞口，则拒绝或截断
        if order.reduce_only {
            let sym = Self::normalize_symbol(&order.symbol);
            let current_pos_qty = {
                let am = self.account_manager.lock().await;
                am.get_position(&sym).map(|p| p.quantity).unwrap_or(0.0)
            };
            let _would_increase = match order.side {
                OrderSide::Buy => current_pos_qty >= 0.0, // 多头增加或从空头反向买入（会减少绝对值），需要细分
                OrderSide::Sell => current_pos_qty <= 0.0,
            };
            // 更精准：reduce-only 仅允许减少现有仓位的绝对值
            let allowed_qty = match order.side {
                OrderSide::Buy => {
                    if current_pos_qty < 0.0 {
                        (-current_pos_qty).min(order.quantity)
                    } else {
                        0.0
                    }
                }
                OrderSide::Sell => {
                    if current_pos_qty > 0.0 {
                        (current_pos_qty).min(order.quantity)
                    } else {
                        0.0
                    }
                }
            };
            if allowed_qty <= 0.0 {
                let mut rejected = order.clone();
                rejected.status = OrderStatus::Rejected;
                self.send_order_update(rejected).await?;
                return Err(BacktestError::Matching(
                    "Reduce-only order would not reduce exposure".to_string(),
                ));
            }
            if allowed_qty < order.quantity {
                let mut rejected = order.clone();
                rejected.status = OrderStatus::Rejected;
                self.send_order_update(rejected).await?;
                return Err(BacktestError::Matching(
                    "Reduce-only order quantity exceeds current position".to_string(),
                ));
            }
        }

        if let Err(e) = self.validate_and_freeze_order_margin(&order).await {
            let mut rejected = order.clone();
            rejected.status = OrderStatus::Rejected;
            self.send_order_update(rejected).await?;
            return Err(e);
        }
        if self.order_latency_simulator.is_enabled() {
            if let Err(e) = self
                .order_latency_simulator
                .add_order(order, self.current_market_timestamp)
            {
                warn!("Failed to add order to latency simulator: {}", e);
            }
        } else {
            self.process_order_immediately(&mut order).await?;
        }
        Ok(())
    }

    async fn process_order_immediately(&mut self, order: &mut Order) -> Result<()> {
        match order.order_type {
            OrderType::Market => self.match_market_order(order).await?,
            OrderType::Limit => self.match_limit_order(order).await?,
            OrderType::LimitIOC => self.match_ioc_order(order).await?,
            OrderType::LimitGTX => self.match_gtx_order(order).await?,
        }
        Ok(())
    }

    async fn match_ioc_order(&mut self, order: &mut Order) -> Result<()> {
        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("IOC order must have price".to_string()))?;
        let can_execute = match order.side {
            OrderSide::Buy => {
                if let Some(bbo) = self.get_current_bbo(&order.symbol) {
                    bbo.ask_price.value() <= order_price.value()
                } else if let Some(best_ask) = self.orderbook.lock().await.best_ask() {
                    best_ask.value() <= order_price.value()
                } else if let Some(cur) = self.get_current_market_price(&order.symbol) {
                    (cur * 1.001) <= order_price.value()
                } else {
                    false
                }
            }
            OrderSide::Sell => {
                if let Some(bbo) = self.get_current_bbo(&order.symbol) {
                    bbo.bid_price.value() >= order_price.value()
                } else if let Some(best_bid) = self.orderbook.lock().await.best_bid() {
                    best_bid.value() >= order_price.value()
                } else if let Some(cur) = self.get_current_market_price(&order.symbol) {
                    (cur * 0.999) >= order_price.value()
                } else {
                    false
                }
            }
        };
        if can_execute {
            let trade_price = match order.side {
                OrderSide::Buy => {
                    if let Some(bbo) = self.get_current_bbo(&order.symbol) {
                        bbo.ask_price
                    } else if let Some(p) = self.orderbook.lock().await.best_ask() {
                        p
                    } else if let Some(cur) = self.get_current_market_price(&order.symbol) {
                        Price::new(cur * 1.001)
                    } else {
                        order_price
                    }
                }
                OrderSide::Sell => {
                    if let Some(bbo) = self.get_current_bbo(&order.symbol) {
                        bbo.bid_price
                    } else if let Some(p) = self.orderbook.lock().await.best_bid() {
                        p
                    } else if let Some(cur) = self.get_current_market_price(&order.symbol) {
                        Price::new(cur * 0.999)
                    } else {
                        order_price
                    }
                }
            };
            // reduce-only数量限制
            if order.reduce_only {
                let sym = Self::normalize_symbol(&order.symbol);
                let pos_qty = {
                    let am = self.account_manager.lock().await;
                    am.get_position(&sym).map(|p| p.quantity).unwrap_or(0.0)
                };
                let allowed = match order.side {
                    OrderSide::Buy => {
                        if pos_qty < 0.0 {
                            (-pos_qty).min(order.quantity)
                        } else {
                            0.0
                        }
                    }
                    OrderSide::Sell => {
                        if pos_qty > 0.0 {
                            (pos_qty).min(order.quantity)
                        } else {
                            0.0
                        }
                    }
                };
                if allowed <= 0.0 {
                    order.status = OrderStatus::Expired;
                    self.release_order_margin(&order.id).await?;
                    self.send_order_update(order.clone()).await?;
                    return Ok(());
                }
                if allowed < order.quantity {
                    order.quantity = allowed;
                }
            }
            let qty = order.quantity;
            self.execute_trade(order, trade_price, qty, false).await?;
            order.status = OrderStatus::Filled;
            self.release_order_margin(&order.id).await?;
        } else {
            order.status = OrderStatus::Expired;
            self.release_order_margin(&order.id).await?;
        }
        info!(
            "IOC execute result: {:?} {:?} {:?}",
            order.client_order_id, order.status, order.timestamp
        );
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    pub async fn process_delayed_orders(&mut self) -> Result<()> {
        let ready = self
            .order_latency_simulator
            .get_ready_orders(self.current_market_timestamp);
        for mut order in ready {
            if let Err(e) = self.process_order_immediately(&mut order).await {
                error!("Failed to process delayed order {}: {}", order.id, e);
            }
        }
        Ok(())
    }

    async fn match_market_order(&mut self, order: &mut Order) -> Result<()> {
        let trade_price = match order.side {
            OrderSide::Buy => {
                if let Some(bbo) = self.get_current_bbo(&order.symbol) {
                    bbo.ask_price
                } else {
                    Price::new(
                        self.get_current_market_price(&order.symbol)
                            .unwrap_or(70000.0)
                            * 1.001,
                    )
                }
            }
            OrderSide::Sell => {
                if let Some(bbo) = self.get_current_bbo(&order.symbol) {
                    bbo.bid_price
                } else {
                    Price::new(
                        self.get_current_market_price(&order.symbol)
                            .unwrap_or(70000.0)
                            * 0.999,
                    )
                }
            }
        };
        // reduce-only数量限制（市价单）
        if order.reduce_only {
            let sym = Self::normalize_symbol(&order.symbol);
            let pos_qty = {
                let am = self.account_manager.lock().await;
                am.get_position(&sym).map(|p| p.quantity).unwrap_or(0.0)
            };
            let allowed = match order.side {
                OrderSide::Buy => {
                    if pos_qty < 0.0 {
                        (-pos_qty).min(order.quantity)
                    } else {
                        0.0
                    }
                }
                OrderSide::Sell => {
                    if pos_qty > 0.0 {
                        (pos_qty).min(order.quantity)
                    } else {
                        0.0
                    }
                }
            };
            if allowed <= 0.0 {
                order.status = OrderStatus::Rejected;
                self.release_order_margin(&order.id).await?;
                self.send_order_update(order.clone()).await?;
                return Ok(());
            }
            if allowed < order.quantity {
                order.quantity = allowed;
            }
        }

        let qty = order.quantity;
        self.execute_trade(order, trade_price, qty, false).await?;
        order.status = OrderStatus::Filled;
        self.release_order_margin(&order.id).await?;
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    async fn match_gtx_order(&mut self, order: &mut Order) -> Result<()> {
        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("GTX order must have price".to_string()))?;
        let would_be_taker = self.is_limit_order_taker(order, order_price)?;
        if would_be_taker {
            info!("GTX order would be taker, cancelling: {:?}", order);
            order.status = OrderStatus::Cancelled;
            self.release_order_margin(&order.id).await?;
        } else {
            order.status = OrderStatus::Pending;
            self.orderbook.lock().await.add_order(order.clone());
            self.pending_orders.insert(order.id.clone(), order.clone());
        }
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    async fn match_limit_order(&mut self, order: &mut Order) -> Result<()> {
        let order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("Limit order must have price".to_string()))?;
        let is_taker = self.is_limit_order_taker(order, order_price)?;
        let mut ob = self.orderbook.lock().await.clone();
        match ob.match_order(order) {
            Ok((matches, remaining)) => {
                for (matched_order, trade_price, trade_qty) in matches {
                    // reduce-only限制：不允许产生反向开仓
                    let exec_qty = if order.reduce_only {
                        let sym = Self::normalize_symbol(&order.symbol);
                        let pos_qty = {
                            let am = self.account_manager.lock().await;
                            am.get_position(&sym).map(|p| p.quantity).unwrap_or(0.0)
                        };
                        match order.side {
                            OrderSide::Buy => {
                                if pos_qty < 0.0 {
                                    (-pos_qty).min(trade_qty)
                                } else {
                                    0.0
                                }
                            }
                            OrderSide::Sell => {
                                if pos_qty > 0.0 {
                                    (pos_qty).min(trade_qty)
                                } else {
                                    0.0
                                }
                            }
                        }
                    } else {
                        trade_qty
                    };
                    if exec_qty > 0.0 {
                        self.execute_trade(order, trade_price, exec_qty, is_taker)
                            .await?;
                    }
                    let mut updated = matched_order;
                    if updated.quantity <= 0.0 {
                        updated.status = OrderStatus::Filled;
                        self.pending_orders.remove(&updated.id);
                        self.release_order_margin(&updated.id).await?;
                    } else {
                        updated.status = OrderStatus::PartiallyFilled;
                        self.pending_orders
                            .insert(updated.id.clone(), updated.clone());
                    }
                    self.send_order_update(updated).await?;
                }
                if remaining <= 0.0 {
                    order.status = OrderStatus::Filled;
                    self.release_order_margin(&order.id).await?;
                } else if remaining < order.quantity {
                    order.status = OrderStatus::PartiallyFilled;
                    order.quantity = remaining;
                    self.orderbook.lock().await.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                } else {
                    order.status = OrderStatus::Pending;
                    self.orderbook.lock().await.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                }
            }
            Err(_) => {
                order.status = OrderStatus::Pending;
                self.orderbook.lock().await.add_order(order.clone());
                self.pending_orders.insert(order.id.clone(), order.clone());
            }
        }
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    pub async fn match_with_bbo(&mut self, bbo: &Bbo) -> Result<()> {
        let mut orders_to_process = Vec::new();
        for (order_id, order) in &self.pending_orders {
            let order_price = order.price.unwrap_or(Price::new(0.0));
            let can_match = match order.side {
                OrderSide::Buy => order_price > bbo.bid_price,
                OrderSide::Sell => order_price < bbo.ask_price,
            };
            if can_match {
                orders_to_process.push((order_id.clone(), order.clone()));
            }
        }
        let mut remove_ids = Vec::new();
        for (order_id, mut order) in orders_to_process {
            let trade_price = order.price.unwrap();
            let qty = if order.reduce_only {
                let sym = Self::normalize_symbol(&order.symbol);
                let pos_qty = {
                    let am = self.account_manager.lock().await;
                    am.get_position(&sym).map(|p| p.quantity).unwrap_or(0.0)
                };
                match order.side {
                    OrderSide::Buy => {
                        if pos_qty < 0.0 {
                            (-pos_qty).min(order.quantity)
                        } else {
                            0.0
                        }
                    }
                    OrderSide::Sell => {
                        if pos_qty > 0.0 {
                            (pos_qty).min(order.quantity)
                        } else {
                            0.0
                        }
                    }
                }
            } else {
                order.quantity
            };
            if qty > 0.0 {
                self.execute_trade(&mut order, trade_price, qty, true)
                    .await?;
                order.quantity -= qty;
                if order.quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                    remove_ids.push(order_id.clone());
                } else {
                    order.status = OrderStatus::PartiallyFilled;
                }
                if let Some(p) = self.pending_orders.get_mut(&order_id) {
                    *p = order.clone();
                }
                self.send_order_update(order).await?;
            } else {
                // reduce-only 无可成交数量，直接推送状态而不成交
                self.send_order_update(order).await?;
            }
        }
        for id in remove_ids {
            self.pending_orders.remove(&id);
            self.orderbook.lock().await.remove_order(&id);
            self.release_order_margin(&id).await?;
        }
        Ok(())
    }

    fn is_limit_order_taker(&self, order: &Order, order_price: Price) -> Result<bool> {
        if let Some(bbo) = self.get_current_bbo(&order.symbol) {
            debug!(
                "gtx order: price: {} bid1: {}, ask1: {}",
                order_price, bbo.bid_price, bbo.ask_price
            );
            match order.side {
                OrderSide::Buy => Ok(order_price.value() >= bbo.ask_price.value()),
                OrderSide::Sell => Ok(order_price.value() <= bbo.bid_price.value()),
            }
        } else {
            Ok(true)
        }
    }

    async fn execute_trade(
        &mut self,
        order: &mut Order,
        price: Price,
        quantity: f64,
        is_maker: bool,
    ) -> Result<()> {
        let market_time = if self.current_market_timestamp > 0 {
            DateTime::from_timestamp_micros(self.current_market_timestamp as i64)
                .unwrap_or_else(|| Utc::now())
        } else {
            order.timestamp
        };
        info!(
            "Matched order: {} at price: {} at {}",
            order.client_order_id, price, market_time
        );
        let trade = Trade {
            id: format!("{}_{}", order.id, self.current_market_timestamp),
            symbol: order.symbol.clone(),
            price,
            quantity,
            side: order.side.clone(),
            timestamp: Some(market_time),
            data_source_type: crate::config::DataSourceType::BinanceTardis,
        };
        let fee_rate = {
            let am = self.account_manager.lock().await;
            if is_maker {
                am.get_maker_fee_rate()
            } else {
                am.get_taker_fee_rate()
            }
        };
        let commission = quantity * price.value() * fee_rate;
        if let Some(ref mut exec) = order.execution_info {
            exec.last_filled_price = Some(price);
            exec.last_filled_quantity = quantity;
            exec.filled_quantity += quantity;
            exec.commission += commission;
            exec.trade_id = Some(trade.id.clone());
            if exec.filled_quantity > 0.0 {
                let total_value = exec
                    .average_price
                    .map_or(0.0, |p| p.value() * (exec.filled_quantity - quantity))
                    + price.value() * quantity;
                exec.average_price = Some(Price::new(total_value / exec.filled_quantity));
            }
        } else {
            order.execution_info = Some(crate::types::OrderExecutionInfo {
                last_filled_price: Some(price),
                last_filled_quantity: quantity,
                filled_quantity: quantity,
                average_price: Some(price),
                commission,
                commission_asset: "USDT".to_string(),
                trade_id: Some(trade.id.clone()),
            });
        }
        {
            let mut am = self.account_manager.lock().await;
            if let Err(e) = am.process_trade(trade.clone(), commission, is_maker) {
                debug!("Failed to process trade in account manager: {}", e);
            }
        }
        if let Err(e) = self.trade_tx.send(trade) {
            return Err(BacktestError::Communication(format!(
                "Failed to send trade: {}",
                e
            )));
        }
        Ok(())
    }

    pub async fn process_cancel_order_request(
        &mut self,
        request: CancelOrderRequest,
    ) -> Result<()> {
        let order_id_opt = if let Some(id) = request.order_id {
            Some(id)
        } else if let Some(cid) = request.client_order_id.as_ref() {
            self.pending_orders
                .values()
                .find(|o| {
                    &o.client_order_id == cid
                        && Self::normalize_symbol(&o.symbol)
                            == Self::normalize_symbol(&request.symbol)
                })
                .map(|o| o.id.clone())
        } else {
            None
        };
        info!("Processing cancel order request: {:?}", order_id_opt);
        if let Some(order_id) = order_id_opt {
            info!(
                "Canceling order: {}",
                request.client_order_id.as_ref().unwrap()
            );
            if let Some(mut order) = self.pending_orders.remove(&order_id) {
                order.status = OrderStatus::Cancelled;
                self.release_order_margin(&order_id).await?;
                self.send_order_update(order).await?;
                info!(
                    "Cancelled order: {}",
                    request.client_order_id.as_ref().unwrap()
                )
            } else {
                info!("Order not found in pending_orders: {}", order_id);
            }
        }
        Ok(())
    }
}
