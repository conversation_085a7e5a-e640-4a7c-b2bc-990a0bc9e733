use crate::account::AccountManager;
use crate::config::{DataSourceType, PlaybackConfig};
use crate::matching::{MultiQueueTimeAligner, OrderBook, OrderManager, RateLimiter};
use crate::types::{CancelOrderRequest, MarketData, Order, Trade};
use crate::Result;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex};
use tokio::time::{Duration, Instant};
use tracing::{debug, error, info, warn};

/// 撮合引擎
pub struct MatchingEngine {
    /// 订单簿
    account_manager: Arc<Mutex<AccountManager>>,
    /// 市场数据输入
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单输入
    order_rx: mpsc::Receiver<Order>,
    /// 取消订单输入
    cancel_order_rx: mpsc::Receiver<CancelOrderRequest>,
    /// 市场数据转发输出（给WebSocket服务器）
    market_data_forward_tx: broadcast::Sender<MarketData>,
    /// 多队列时间对齐器
    time_aligner: MultiQueueTimeAligner,
    /// 速率限制器
    rate_limiter: RateLimiter,
    /// 订单与状态管理器
    order_manager: OrderManager,
}

impl MatchingEngine {
    /// 创建新的撮合引擎（带回放配置）
    pub fn new_with_playback_config(
        orderbook: Arc<Mutex<OrderBook>>,
        account_manager: Arc<Mutex<AccountManager>>,
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        cancel_order_rx: mpsc::Receiver<CancelOrderRequest>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        market_data_forward_tx: broadcast::Sender<MarketData>,
        playback_config: PlaybackConfig,
    ) -> Self {
        let time_aligner = MultiQueueTimeAligner::new(playback_config.time_alignment.clone());
        let rate_limiter = RateLimiter::new(playback_config.clone());

        let order_manager = OrderManager::new(
            orderbook.clone(),
            account_manager.clone(),
            trade_tx.clone(),
            order_update_tx.clone(),
            playback_config.clone(),
        );

        Self {
            account_manager,
            market_data_rx,
            order_rx,
            cancel_order_rx,
            market_data_forward_tx,
            time_aligner,
            rate_limiter,
            order_manager,
        }
    }

    /// 启动撮合引擎
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting matching engine");

        // 检查订单通道是否已关闭

        let mut order_channel_closed = false;
        let mut cancel_order_channel_closed = false;

        loop {
            tokio::select! {
                biased;

                // 优先处理新订单（如果通道还开着）
                order = self.order_rx.recv(), if !order_channel_closed => {
                    match order {
                        Some(order) => {
                            if let Err(e) = self.order_manager.process_order(order).await {
                                error!("Failed to process order: {}", e);
                            }
                        }
                        None => {
                            info!("Order channel closed");
                            order_channel_closed = true;
                            // 不退出循环，继续处理其他事件
                        }
                    }
                }

                // 其次处理取消订单请求（如果通道还开着）
                cancel_request = self.cancel_order_rx.recv(), if !cancel_order_channel_closed => {
                    match cancel_request {
                        Some(request) => {
                            if let Err(e) = self.order_manager.process_cancel_order_request(request).await {
                                error!("Failed to process cancel order request: {}", e);
                            }
                        }
                        None => {
                            info!("Cancel order channel closed");
                            cancel_order_channel_closed = true;
                            // 不退出循环，继续处理其他事件
                        }
                    }
                }

                // 最后处理市场数据
                market_data = self.market_data_rx.recv() => {
                    match market_data {
                        Ok(data) => {
                            if let Err(e) = self.process_market_data(data).await {
                                error!("Failed to process market data: {}", e);
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            warn!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }
            }
        }

        // 在停止前处理缓冲区中剩余的数据
        info!("Processing remaining buffered data before stopping");
        self.process_buffered_data().await?;

        info!("Matching engine stopped");
        Ok(())
    }

    /// 处理市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        debug!("Received market data in matching engine: {:?}", market_data);

        self.time_aligner.add_data(market_data)?;

        debug!(
            "Added data to time aligner, total size: {}",
            self.time_aligner.total_size()
        );

        // 检查是否应该处理队列中的数据
        self.check_and_process_queues().await?;

        Ok(())
    }

    /// 直接添加数据到队列而不处理（用于测试）
    #[cfg(test)]
    pub async fn add_to_buffer_only(&mut self, market_data: MarketData) -> Result<()> {
        self.time_aligner.add_data(market_data)?;
        Ok(())
    }

    /// 检查并处理队列中的数据
    /// 使用智能的多队列时间对齐策略（带订单/撤单抢占与时间预算）
    async fn check_and_process_queues(&mut self) -> Result<()> {
        if !self.time_aligner.has_data() {
            return Ok(());
        }

        // 检查是否应该处理数据
        if self.time_aligner.should_process_data() {
            let batch_size = self.rate_limiter.get_batch_size() as usize;
            let budget = Duration::from_millis(20); // 单次批处理的最大墙钟预算
            let start = Instant::now();
            let mut processed = 0usize;

            // 逐条拉取与处理，确保不会因提前 break 丢失数据
            while processed < batch_size && self.time_aligner.has_data() {
                if let Some(data) = self.time_aligner.get_next_data() {
                    debug!("Processing market data: {:?}", data);
                    self.process_single_market_data_internal(data).await?;
                    processed += 1;
                    self.drain_control_channels().await?;
                    if start.elapsed() >= budget {
                        debug!(
                            "Batch time budget reached ({} items in {:?}), yielding",
                            processed,
                            start.elapsed()
                        );
                        break;
                    }
                } else {
                    break;
                }
            }
        }

        Ok(())
    }
    /// 非阻塞地抢占处理订单与撤单通道，避免在市场数据批处理期间长时间饥饿
    async fn drain_control_channels(&mut self) -> Result<()> {
        // 尝试尽可能多地拉取订单
        loop {
            match self.order_rx.try_recv() {
                Ok(order) => {
                    let current_bbo = self.order_manager.get_current_bbo(&order.symbol).unwrap();
                    info!(
                        "current bbo: {} bid1: {} ask1: {}",
                        current_bbo.timestamp_datetime().unwrap(),
                        current_bbo.bid_price,
                        current_bbo.ask_price
                    );
                    info!("Draining order channel: {:?}", order.client_order_id);
                    if let Err(e) = self.order_manager.process_order(order).await {
                        error!("Failed to process order during drain: {}", e);
                    }
                }
                Err(tokio::sync::mpsc::error::TryRecvError::Empty) => break,
                Err(tokio::sync::mpsc::error::TryRecvError::Disconnected) => {
                    // 通道关闭，停止进一步尝试
                    break;
                }
            }
        }

        // 尝试尽可能多地拉取撤单
        loop {
            match self.cancel_order_rx.try_recv() {
                Ok(request) => {
                    info!(
                        "Draining cancel order channel: {:?}",
                        request.client_order_id
                    );
                    if let Err(e) = self
                        .order_manager
                        .process_cancel_order_request(request)
                        .await
                    {
                        error!("Failed to process cancel during drain: {}", e);
                    }
                }
                Err(tokio::sync::mpsc::error::TryRecvError::Empty) => break,
                Err(tokio::sync::mpsc::error::TryRecvError::Disconnected) => {
                    break;
                }
            }
        }

        Ok(())
    }

    /// 获取时间对齐器的总数据量（用于测试）
    #[cfg(test)]
    pub fn get_time_aligner_size(&self) -> usize {
        self.time_aligner.total_size()
    }

    /// 获取时间对齐器的总数据量（公开方法）
    pub fn get_buffer_size(&self) -> usize {
        self.time_aligner.total_size()
    }

    /// 检查缓冲区是否为空
    pub fn is_buffer_empty(&self) -> bool {
        !self.time_aligner.has_data()
    }

    /// 更新回放配置
    pub fn update_playback_config(&mut self, config: PlaybackConfig) {
        info!(
            "Updating playback config: rate_per_second={}, enabled={}, batch_size={}",
            config.rate_per_second, config.enabled, config.batch_size
        );

        // 更新时间对齐器配置
        self.time_aligner
            .update_config(config.time_alignment.clone());

        // 更新速率限制器配置
        self.rate_limiter.update_config(config.clone());

        // 更新订单延迟模拟器配置
        self.order_manager
            .update_latency_config(config.order_latency.clone());

        info!(
            "Order latency simulation: enabled={}, latency_micros={}, random_latency={}",
            config.order_latency.enabled,
            config.order_latency.latency_micros,
            config.order_latency.random_latency
        );
    }

    /// 获取当前回放配置
    pub fn get_playback_config(&self) -> &PlaybackConfig {
        self.rate_limiter.get_config()
    }

    /// 获取订单延迟模拟器统计信息
    pub fn get_order_latency_stats(&self) -> crate::matching::OrderLatencyStats {
        self.order_manager.get_order_latency_stats()
    }

    /// 获取当前市场时间戳
    pub fn get_current_market_timestamp(&self) -> u64 {
        self.order_manager.get_current_market_timestamp()
    }

    /// 清空延迟订单队列
    pub fn clear_delayed_orders(&mut self) {
        self.order_manager.clear_delayed_orders();
    }

    pub async fn process_buffered_data(&mut self) -> Result<()> {
        while let Some(data) = self.time_aligner.get_next_data() {
            self.process_single_market_data_internal(data).await?;
            self.drain_control_channels().await?;
        }
        Ok(())
    }

    /// 处理单个市场数据项
    async fn process_single_market_data_internal(&mut self, market_data: MarketData) -> Result<()> {
        // 先按配置应用节拍（自然速度或固定速率）
        let ts_us = market_data.timestamp_for_sorting();
        self.rate_limiter.apply_pacing_for_timestamp(ts_us).await?;
        debug!(
            "market data timestamp: {}",
            market_data.timestamp_datetime().unwrap()
        );

        // 始终根据输入数据更新时间戳、BBO缓存、账户价格，避免仅有官方数据(如BookTicker)时未实现盈亏不更新
        if market_data.data_source_type() == DataSourceType::BinanceTardis {
            self.order_manager.set_current_market_timestamp(ts_us);
            if let Some(ts) = market_data.timestamp_datetime() {
                debug!(
                    "Current market timestamp updated to: {}",
                    ts.format("%Y-%m-%d %H:%M:%S%.6f")
                );
            }
            self.order_manager
                .update_bbo_cache_from_market_data(&market_data)
                .await;
            self.order_manager.process_delayed_orders().await?;
            self.order_manager.update_account_prices(&market_data).await;

            debug!("✅ Processing market data for matching");
            match &market_data {
                MarketData::OrderBook(_) => {
                    debug!("Processing orderbook snapshot");
                }
                MarketData::Bbo(bbo) => {
                    debug!("Processing BBO update with timestamp: {:?}", bbo.timestamp);
                    debug!(
                        "📊 Processing BBO for matching: bid={}, ask={}",
                        bbo.bid_price, bbo.ask_price
                    );
                    // 使用BBO进行撮合（BBO缓存已在前面更新）
                    debug!("matching with bbo: {:?}", bbo);
                    self.order_manager.match_with_bbo(bbo).await?;
                }
                MarketData::TradeData(trade_data) => {
                    debug!(
                        "Processing TradeData: id={} at timestamp={}",
                        trade_data.id, trade_data.timestamp
                    );
                    // 交易数据通常用于验证撮合结果，这里可以添加相关逻辑
                }
            }
        }

        // 克隆市场数据用于记录
        let market_data_for_recording = market_data.clone();
        match self.market_data_forward_tx.send(market_data) {
            Ok(receiver_count) => {
                debug!(
                    "✅ Market data forwarded to {} WebSocket receivers",
                    receiver_count
                );
            }
            Err(e) => {
                info!("❌ Failed to forward market data to WebSocket: {}", e);
                // 这里不返回错误，因为WebSocket转发失败不应该影响撮合引擎的正常运行
            }
        }

        // 记录处理了一个数据项
        self.rate_limiter.record_processed();

        // 记录市场数据到回测记录器（在流控之后）
        self.record_market_data_after_flow_control(&market_data_for_recording)
            .await;

        Ok(())
    }

    /// 获取账户余额
    pub async fn get_account_balance(&self, asset: &str) -> f64 {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_balance(asset)
    }

    /// 获取账户净值
    pub async fn get_account_net_value(&self) -> f64 {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_net_value()
    }

    /// 获取仓位信息
    pub async fn get_position(&self, symbol: &str) -> Option<crate::account::position::Position> {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_position(symbol).cloned()
    }

    /// 记录市场数据到回测记录器（在流控之后）
    async fn record_market_data_after_flow_control(&self, market_data: &MarketData) {
        use crate::state::get_backtest_recorder;

        // 只记录BBO数据
        if let MarketData::Bbo(bbo) = market_data {
            if let Some(recorder) = get_backtest_recorder().await {
                let mut recorder = recorder.lock().await;
                recorder.record_bbo(bbo);
            }
        }
    }
}
