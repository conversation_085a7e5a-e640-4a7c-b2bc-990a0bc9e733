use crate::{
    account::account::AccountSummary,
    types::{BacktestBbo, BacktestOrder, BacktestSummary, BacktestTrade, OrderSide},
};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use tokio::sync::Mutex;

/// HTML生成器
pub struct HtmlGenerator;

/// 实时报告生成器
pub struct RealTimeReportGenerator {
    /// 报告更新间隔（秒）
    update_interval: u64,
    /// 最后更新时间
    last_update: Option<DateTime<Utc>>,
    /// 报告缓存
    cached_report: Option<String>,
}

impl RealTimeReportGenerator {
    /// 创建新的实时报告生成器
    pub fn new(update_interval: u64) -> Self {
        Self {
            update_interval,
            last_update: None,
            cached_report: None,
        }
    }

    /// 检查是否需要更新报告
    pub fn should_update(&self) -> bool {
        match self.last_update {
            Some(last) => {
                let now = Utc::now();
                let duration = now - last;
                duration.num_seconds() >= self.update_interval as i64
            }
            None => true,
        }
    }

    /// 生成实时报告
    pub async fn generate_real_time_report(
        &mut self,
        recorder: &Arc<Mutex<crate::types::BacktestRecorder>>,
    ) -> String {
        // 检查是否需要更新
        if !self.should_update() {
            if let Some(cached) = &self.cached_report {
                return cached.clone();
            }
        }

        // 生成新的报告
        let recorder_guard = recorder.lock().await;
        let report = if recorder_guard.is_recording {
            // 获取账户统计（用于实时的已实现/未实现盈亏）
            if let Some(account_manager) = crate::state::get_account_manager().await {
                let manager = account_manager.lock().await;
                let summary = manager.get_account_summary();
                Self::generate_live_report(&recorder_guard, summary)
            } else {
                Self::generate_no_data_report()
            }
        } else {
            // 如果回测已结束，生成完整报告
            match recorder_guard.generate_summary_async().await {
                Some(summary) => HtmlGenerator::generate_summary_html(&summary),
                None => Self::generate_no_data_report(),
            }
        };

        // 更新缓存和时间戳
        self.cached_report = Some(report.clone());
        self.last_update = Some(Utc::now());

        report
    }

    fn generate_live_report(
        recorder: &crate::types::BacktestRecorder,
        summary: AccountSummary,
    ) -> String {
        tracing::trace!("generate_live_report: {:?}", summary);
        let current_time = summary.last_updated;
        let start_time = recorder.start_time.unwrap_or(current_time);

        // 计算实时统计信息
        let total_pnl = summary.stats.realized_pnl + summary.stats.unrealized_pnl;
        let total_orders = recorder.orders.len();
        let total_fills = recorder.trades.len();

        let position = match summary.positions.get(0) {
            Some(position) => position.quantity,
            None => 0.0,
        };

        // 生成实时K线数据（优先使用OHLC聚合数据）
        let kline_data = if recorder.bbo_aggregator.total_count() > 0 {
            HtmlGenerator::generate_kline_data_from_ohlc(
                recorder.bbo_aggregator.get_completed_ohlc(),
                recorder.bbo_aggregator.get_current_ohlc(),
                &recorder.orders,
            )
        } else {
            HtmlGenerator::generate_kline_data(&recorder.bbo_records, &recorder.orders)
        };
        let summary_stats = Self::generate_live_summary_stats(
            start_time,
            current_time,
            total_pnl,
            summary.stats.realized_pnl,
            summary.stats.unrealized_pnl,
            total_orders,
            total_fills,
            summary.risk_metrics.win_rate,
            summary.risk_metrics.interval_return,
            summary.risk_metrics.annual_return_estimate,
            summary.risk_metrics.max_drawdown,
            position,
        );
        let trade_table = HtmlGenerator::generate_trade_table(&recorder.trades);
        let order_table = HtmlGenerator::generate_order_table(&recorder.orders);

        let refresh_interval_secs = 5;

        format!(
            r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时回测报告</title>
    <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 1.2em;
            font-weight: 400;
        }}
        .header p {{
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 0.8em;
        }}
        .live-indicator {{
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 10px;
            animation: pulse 2s infinite;
        }}
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.7; }}
            100% {{ opacity: 1; }}
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-bottom: 1px solid #667eea;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-size: 1.2em;
        }}
        .stats-grid {{
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
            justify-content: space-between;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 120px;
            max-width: 180px;
        }}
        .stat-card h3 {{
            margin: 0 0 2px 0;
            font-size: 0.7em;
            font-weight: 400;
            opacity: 0.9;
        }}
        .stat-card .value {{
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 1px;
            line-height: 1.2;
        }}
        .stat-card .label {{
            font-size: 0.6em;
            opacity: 0.8;
            line-height: 1;
        }}
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            padding-bottom: 0px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            min-height: 400px;
            overflow: visible;
        }}
        #klineChart {{
            margin-bottom: 20px;
            overflow: visible;
        }}
        .tv-lightweight-charts {{
            overflow: visible !important;
        }}
        .table-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .positive {{
            color: #28a745;
            font-weight: bold;
        }}
        .negative {{
            color: #dc3545;
            font-weight: bold;
        }}
        .buy {{
            color: #28a745;
        }}
        .sell {{
            color: #dc3545;
        }}
        .status-filled {{
            background-color: #d4edda;
        }}
        .status-partial {{
            background-color: #fff3cd;
        }}
        .status-cancelled {{
            background-color: #f8d7da;
            opacity: 0.7;
        }}
        .status-rejected {{
            background-color: #f5c6cb;
            opacity: 0.8;
        }}
        .status-other {{
            background-color: #e2e3e5;
        }}
        .timestamp {{
            font-family: monospace;
            font-size: 0.9em;
        }}
        .chart-legend {{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
        }}
        .legend-symbol {{
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            border-radius: 2px;
        }}
        .legend-buy {{ background: #2196F3; color: white; }}
        .legend-sell {{ background: #e91e63; color: white; }}
        .legend-partial-buy {{ background: #64B5F6; color: white; }}
        .legend-partial-sell {{ background: #F48FB1; color: white; }}
        .legend-cancelled {{ background: #9E9E9E; color: white; }}
        .legend-rejected {{ background: #F44336; color: white; }}
        .auto-refresh {{
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>实时回测报告</h1>
            <p>回测期间: {} - {}</p>
            <div class="live-indicator">🔴 实时更新中</div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📊 实时统计摘要</h2>
                {}
            </div>

            <div class="section">
                <h2>📈 K线图与交易点位</h2>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span class="legend-symbol legend-buy">↑</span>
                        <span>B - 买入成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-sell">↓</span>
                        <span>S - 卖出成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-partial-buy">↑</span>
                        <span>PB - 买入部分成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-partial-sell">↓</span>
                        <span>PS - 卖出部分成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-cancelled">↑</span>
                        <span>CB - 买入取消</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-cancelled">↓</span>
                        <span>CS - 卖出取消</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-rejected">×</span>
                        <span>X - 订单拒绝</span>
                    </div>
                </div>
                <div class="chart-container">
                    <label style="display:flex;align-items:center;gap:6px;margin:0 0 8px 4px;">
                        <input id="toggle-cancelled" type="checkbox" /> 显示已取消订单
                    </label>
                    <div id="klineChart"></div>
                </div>
            </div>

            <div class="section">
                <h2>💰 交易记录</h2>
                <div class="table-container">
                    {}
                </div>
            </div>

            <div class="section">
                <h2>📋 订单记录</h2>
                <div class="table-container">
                    {}
                </div>
            </div>
        </div>
    </div>

    <div class="auto-refresh">
        <p>⏰ 报告将每{}秒自动更新 | 最后更新时间: {}</p>
    </div>

    <script>
        // K线图数据
        {}

        // 创建TradingView K线图
        function createKlineChart() {{
            const chartContainer = document.getElementById('klineChart');

            // 清空容器并设置样式
            chartContainer.innerHTML = '';
            chartContainer.style.height = '400px';
            chartContainer.style.width = '100%';

            // 等待库加载完成并创建图表
            if (typeof LightweightCharts === 'undefined') {{
                chartContainer.innerHTML = '<p style="text-align: center; padding: 50px;">正在加载图表库...</p>';
                setTimeout(() => {{ location.reload(); }}, 2000);
                return;
            }}

        try {{
            // 创建图表
            const chart = LightweightCharts.createChart(chartContainer, {{
                width: chartContainer.clientWidth,
                height: 380,
                layout: {{
                    backgroundColor: '#ffffff',
                    textColor: '#333333',
                    fontSize: 12
                }},
                handleScroll: {{
                    mouseWheel: true,
                    pressedMouseMove: true,
                    horzTouchDrag: true,
                    vertTouchDrag: true
                }},
                grid: {{
                    vertLines: {{
                        color: '#e1e1e1'
                    }},
                    horzLines: {{
                        color: '#e1e1e1'
                    }}
                }},
                crosshair: {{
                    mode: LightweightCharts.CrosshairMode.Normal,
                    vertLine: {{
                        width: 1,
                        color: 'rgba(224, 227, 235, 0.5)',
                        style: LightweightCharts.LineStyle.Dashed
                    }},
                    horzLine: {{
                        width: 1,
                        color: 'rgba(224, 227, 235, 0.5)',
                        style: LightweightCharts.LineStyle.Dashed
                    }}
                }},
                rightPriceScale: {{
                    borderColor: '#cccccc'
                }},
                timeScale: {{
                    borderColor: '#cccccc',
                    timeVisible: true,
                    secondsVisible: true,
                    fixLeftEdge: false,
                    fixRightEdge: false,
                    borderVisible: true,
                    visible: true
                }}
            }});

        // 添加K线系列
        const candlestickSeries = chart.addCandlestickSeries({{
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderDownColor: '#ef5350',
            borderUpColor: '#26a69a',
            wickDownColor: '#ef5350',
            wickUpColor: '#26a69a',
            priceFormat: {{ type: 'price', precision: pricePrecision, minMove: priceMinMove }}
        }});

            // 设置K线数据
            if (candlestickData && candlestickData.length > 0) {{
                candlestickSeries.setData(candlestickData);
            }}

            // 添加交易标记（默认不含取消）
            candlestickSeries.setMarkers(typeof allMarkersDefault !== 'undefined' ? allMarkersDefault : []);

            // 绑定“显示已取消订单”切换
            const toggle = document.getElementById('toggle-cancelled');
            if (toggle) {{
                toggle.addEventListener('change', () => {{
                    candlestickSeries.setMarkers(toggle.checked ? allMarkersWithCancelled : allMarkersDefault);
                }});
            }}

            // 自适应图表大小
            chart.timeScale().fitContent();

            // 响应式调整
            window.addEventListener('resize', () => {{
                chart.applyOptions({{ width: chartContainer.clientWidth }});
            }});
        }} catch (error) {{
            console.error('创建图表时出错:', error);
            chartContainer.innerHTML = '<p style="text-align: center; padding: 50px; color: red;">图表加载失败: ' + error.message + '</p>';
        }}
        }}

        // 调用函数创建图表
        createKlineChart();

        // 自动刷新页面
        setTimeout(function() {{
            location.reload();
        }}, {}000);
    </script>
</body>
</html>
"#,
            start_time.format("%Y-%m-%d %H:%M:%S"),
            current_time.format("%Y-%m-%d %H:%M:%S"),
            summary_stats,
            trade_table,
            order_table,
            refresh_interval_secs,
            current_time.format("%Y-%m-%d %H:%M:%S"),
            kline_data,
            refresh_interval_secs
        )
    }

    /// 生成实时统计摘要
    fn generate_live_summary_stats(
        _start_time: DateTime<Utc>,
        _current_time: DateTime<Utc>,
        total_pnl: f64,
        realized_pnl: f64,
        unrealized_pnl: f64,
        total_orders: usize,
        total_fills: usize,
        win_rate: f64,
        _interval_return: f64,
        annual_return_estimate: f64,
        max_drawdown: f64,
        position_count: f64,
    ) -> String {
        format!(
            r#"
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>总盈亏</h3>
                    <div class="value {}">{:.2} USDT</div>
                    <div class="label">累计收益</div>
                </div>
                <div class="stat-card">
                    <h3>已实现盈亏</h3>
                    <div class="value {}">{:.2} USDT</div>
                    <div class="label">Realized PnL</div>
                </div>
                <div class="stat-card">
                    <h3>未实现盈亏</h3>
                    <div class="value {}">{:.2} USDT</div>
                    <div class="label">Unrealized PnL</div>
                </div>
                <div class="stat-card">
                    <h3>年化收益率</h3>
                    <div class="value {}">{:.2}%</div>
                    <div class="label">年化计算</div>
                </div>
                <div class="stat-card">
                    <h3>胜率</h3>
                    <div class="value">{:.1}%</div>
                    <div class="label">盈利交易占比</div>
                </div>
                <div class="stat-card">
                    <h3>最大回撤</h3>
                    <div class="value">{:.2}%</div>
                    <div class="label">最大亏损幅度</div>
                </div>
                <div class="stat-card">
                    <h3>下单次数</h3>
                    <div class="value">{}</div>
                    <div class="label">总订单数量</div>
                </div>
                <div class="stat-card">
                    <h3>成交次数</h3>
                    <div class="value">{}</div>
                    <div class="label">成功成交数量</div>
                </div>
                <div class="stat-card">
                    <h3>持仓数量</h3>
                    <div class="value">{}</div>
                    <div class="label">当前持仓数</div>
                </div>
            </div>
            "#,
            if total_pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            total_pnl,
            if realized_pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            realized_pnl,
            if unrealized_pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            unrealized_pnl,
            if annual_return_estimate >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            annual_return_estimate,
            win_rate,
            max_drawdown,
            total_orders,
            total_fills,
            position_count
        )
    }

    /// 生成无数据报告
    fn generate_no_data_report() -> String {
        r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测报告 - 无数据</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
        }
        .no-data {
            color: #666;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>回测报告</h1>
        <div class="no-data">
            <p>暂无回测数据</p>
            <p>请先开始回测记录</p>
        </div>
    </div>
</body>
</html>
        "#
        .to_string()
    }
}

impl HtmlGenerator {
    /// 生成完整的回测总结HTML页面
    pub fn generate_summary_html(summary: &BacktestSummary) -> String {
        let kline_data = if !summary.ohlc_data.is_empty() {
            Self::generate_kline_data_from_ohlc(&summary.ohlc_data, None, &summary.orders)
        } else {
            Self::generate_kline_data(&summary.bbo_records, &summary.orders)
        };
        let summary_stats = Self::generate_summary_stats(summary);
        let trade_table = Self::generate_trade_table(&summary.trades);
        let order_table = Self::generate_order_table(&summary.orders);

        format!(
            r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测总结报告</title>
    <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 1.2em;
            font-weight: 400;
        }}
        .header p {{
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 0.8em;
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-bottom: 1px solid #667eea;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-size: 1.2em;
        }}
        .stats-grid {{
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
            justify-content: space-between;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 120px;
            max-width: 180px;
        }}
        .stat-card h3 {{
            margin: 0 0 2px 0;
            font-size: 0.7em;
            font-weight: 400;
            opacity: 0.9;
        }}
        .stat-card .value {{
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 1px;
            line-height: 1.2;
        }}
        .stat-card .label {{
            font-size: 0.6em;
            opacity: 0.8;
            line-height: 1;
        }}
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            padding-bottom: 0px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            min-height: 400px;
            overflow: visible;
        }}
        #klineChart {{
            margin-bottom: 20px;
            overflow: visible;
        }}
        .tv-lightweight-charts {{
            overflow: visible !important;
        }}
        .table-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .positive {{
            color: #28a745;
            font-weight: bold;
        }}
        .negative {{
            color: #dc3545;
            font-weight: bold;
        }}
        .buy {{
            color: #28a745;
        }}
        .sell {{
            color: #dc3545;
        }}
        .status-filled {{
            background-color: #d4edda;
        }}
        .status-partial {{
            background-color: #fff3cd;
        }}
        .status-cancelled {{
            background-color: #f8d7da;
            opacity: 0.7;
        }}
        .status-rejected {{
            background-color: #f5c6cb;
            opacity: 0.8;
        }}
        .status-other {{
            background-color: #e2e3e5;
        }}
        .timestamp {{
            font-family: monospace;
            font-size: 0.9em;
        }}
        .chart-legend {{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
        }}
        .legend-symbol {{
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            border-radius: 2px;
        }}
        .legend-buy {{ background: #2196F3; color: white; }}
        .legend-sell {{ background: #e91e63; color: white; }}
        .legend-partial-buy {{ background: #64B5F6; color: white; }}
        .legend-partial-sell {{ background: #F48FB1; color: white; }}
        .legend-cancelled {{ background: #9E9E9E; color: white; }}
        .legend-rejected {{ background: #F44336; color: white; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>回测总结报告</h1>
            <p>回测期间: {} - {}</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📊 统计摘要</h2>
                {}
            </div>

            <div class="section">
                <h2>📈 K线图与交易点位</h2>
                <div class="chart-legend">
                    <div class="legend-item">
                        <span class="legend-symbol legend-buy">↑</span>
                        <span>B - 买入成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-sell">↓</span>
                        <span>S - 卖出成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-partial-buy">↑</span>
                        <span>PB - 买入部分成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-partial-sell">↓</span>
                        <span>PS - 卖出部分成交</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-cancelled">↑</span>
                        <span>CB - 买入取消</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-cancelled">↓</span>
                        <span>CS - 卖出取消</span>
                    </div>
                    <div class="legend-item">
                        <span class="legend-symbol legend-rejected">×</span>
                        <span>X - 订单拒绝</span>
                    </div>
                </div>
                <div class="chart-container">
                    <label style="display:flex;align-items:center;gap:6px;margin:0 0 8px 4px;">
                        <input id="toggle-cancelled" type="checkbox" /> 显示已取消订单
                    </label>
                    <div id="klineChart"></div>
                </div>
            </div>

            <div class="section">
                <h2>💰 交易记录</h2>
                <div class="table-container">
                    {}
                </div>
            </div>

            <div class="section">
                <h2>📋 订单记录</h2>
                <div class="table-container">
                    {}
                </div>
            </div>
        </div>
    </div>

    <script>
        // K线图数据
        {}

        // 创建TradingView K线图
        function createKlineChart() {{
            const chartContainer = document.getElementById('klineChart');

            // 清空容器并设置样式
            chartContainer.innerHTML = '';
            chartContainer.style.height = '400px';
            chartContainer.style.width = '100%';

            // 等待库加载完成并创建图表
            if (typeof LightweightCharts === 'undefined') {{
                chartContainer.innerHTML = '<p style="text-align: center; padding: 50px;">正在加载图表库...</p>';
                setTimeout(() => {{ location.reload(); }}, 2000);
                return;
            }}

        try {{
            // 创建图表
            const chart = LightweightCharts.createChart(chartContainer, {{
                width: chartContainer.clientWidth,
                height: 380,
                layout: {{
                    backgroundColor: '#ffffff',
                    textColor: '#333333'
                }},
                handleScroll: {{
                    mouseWheel: true,
                    pressedMouseMove: true,
                    horzTouchDrag: true,
                    vertTouchDrag: true
                }},
                grid: {{
                    vertLines: {{
                        color: '#e1e1e1'
                    }},
                    horzLines: {{
                        color: '#e1e1e1'
                    }}
                }},
                crosshair: {{
                    mode: LightweightCharts.CrosshairMode.Normal
                }},
                rightPriceScale: {{
                    borderColor: '#cccccc'
                }},
                timeScale: {{
                    borderColor: '#cccccc',
                    timeVisible: true,
                    secondsVisible: true,
                    fixLeftEdge: false,
                    fixRightEdge: false,
                    borderVisible: true,
                    visible: true
                }}
            }});

        // 添加K线系列
        const candlestickSeries = chart.addCandlestickSeries({{
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderDownColor: '#ef5350',
            borderUpColor: '#26a69a',
            wickDownColor: '#ef5350',
            wickUpColor: '#26a69a',
            priceFormat: {{ type: 'price', precision: pricePrecision, minMove: priceMinMove }}
        }});

            // 设置K线数据
            if (candlestickData && candlestickData.length > 0) {{
                candlestickSeries.setData(candlestickData);
            }}

            // 添加交易标记（默认不含取消）
            candlestickSeries.setMarkers(typeof allMarkersDefault !== 'undefined' ? allMarkersDefault : []);

            // 绑定“显示已取消订单”切换
            const toggle = document.getElementById('toggle-cancelled');
            if (toggle) {{
                toggle.addEventListener('change', () => {{
                    candlestickSeries.setMarkers(toggle.checked ? allMarkersWithCancelled : allMarkersDefault);
                }});
            }}

            // 自适应图表大小
            chart.timeScale().fitContent();

            // 响应式调整
            window.addEventListener('resize', () => {{
                chart.applyOptions({{ width: chartContainer.clientWidth }});
            }});
        }} catch (error) {{
            console.error('创建图表时出错:', error);
            chartContainer.innerHTML = '<p style="text-align: center; padding: 50px; color: red;">图表加载失败: ' + error.message + '</p>';
        }}
        }}

        // 调用函数创建图表
        createKlineChart();
    </script>
</body>
</html>
"#,
            summary.start_time.format("%Y-%m-%d %H:%M:%S"),
            summary.end_time.format("%Y-%m-%d %H:%M:%S"),
            summary_stats,
            trade_table,
            order_table,
            kline_data
        )
    }

    /// 生成统计摘要HTML
    fn generate_summary_stats(summary: &BacktestSummary) -> String {
        // 生成持仓详情HTML
        let positions_html = if summary.positions.is_empty() {
            String::new()
        } else {
            let mut positions_rows = String::new();
            for (symbol, quantity) in &summary.positions {
                positions_rows.push_str(&format!(
                    r#"
                    <tr>
                        <td>{}</td>
                        <td class="{}">{:.8}</td>
                    </tr>
                    "#,
                    symbol,
                    if *quantity >= 0.0 {
                        "positive"
                    } else {
                        "negative"
                    },
                    quantity
                ));
            }

            format!(
                r#"
                <div class="section">
                    <h2>📊 持仓详情</h2>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>持仓数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {}
                            </tbody>
                        </table>
                    </div>
                </div>
                "#,
                positions_rows
            )
        };

        format!(
            r#"
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>总盈亏</h3>
                    <div class="value {}">{:.2} USDT</div>
                    <div class="label">累计收益</div>
                </div>
                <div class="stat-card">
                    <h3>已实现盈亏</h3>
                    <div class="value {}">{:.2} USDT</div>
                    <div class="label">Realized PnL</div>
                </div>
                <div class="stat-card">
                    <h3>未实现盈亏</h3>
                    <div class="value {}">{:.2} USDT</div>
                    <div class="label">Unrealized PnL</div>
                </div>
                <div class="stat-card">
                    <h3>年化收益率</h3>
                    <div class="value {}">{:.2}%</div>
                    <div class="label">年化表现</div>
                </div>
                <div class="stat-card">
                    <h3>胜率</h3>
                    <div class="value">{:.1}%</div>
                    <div class="label">盈利交易占比</div>
                </div>
                <div class="stat-card">
                    <h3>最大回撤</h3>
                    <div class="value negative">{:.2}%</div>
                    <div class="label">最大亏损幅度</div>
                </div>
                <div class="stat-card">
                    <h3>下单次数</h3>
                    <div class="value">{}</div>
                    <div class="label">总订单数</div>
                </div>
                <div class="stat-card">
                    <h3>成交次数</h3>
                    <div class="value">{}</div>
                    <div class="label">成功交易数</div>
                </div>
                <div class="stat-card">
                    <h3>持仓品种数</h3>
                    <div class="value">{}</div>
                    <div class="label">当前持仓品种数</div>
                </div>
            </div>
            {}
            "#,
            if summary.total_pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            summary.total_pnl,
            if summary.realized_pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            summary.realized_pnl,
            if summary.unrealized_pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            summary.unrealized_pnl,
            if summary.annual_return >= 0.0 {
                "positive"
            } else {
                "negative"
            },
            summary.annual_return,
            summary.win_rate,
            summary.max_drawdown,
            summary.total_orders,
            summary.total_fills,
            summary.position_count,
            positions_html
        )
    }

    /// 生成交易记录表格HTML
    fn generate_trade_table(trades: &[BacktestTrade]) -> String {
        let mut rows = String::new();

        for trade in trades {
            let side_class = match trade.side {
                OrderSide::Buy => "buy",
                OrderSide::Sell => "sell",
            };

            let pnl_class = if trade.pnl >= 0.0 {
                "positive"
            } else {
                "negative"
            };

            rows.push_str(&format!(
                r#"
                <tr>
                    <td class="timestamp">{}</td>
                    <td class="{}">{:?}</td>
                    <td>{}</td>
                    <td>{:.4}</td>
                    <td>{:.4} {}</td>
                    <td class="{}">{:.2}</td>
                </tr>
                "#,
                trade.timestamp.format("%Y-%m-%d %H:%M:%S"),
                side_class,
                trade.side,
                trade.price.value(),
                trade.quantity,
                trade.fee,
                trade.fee_asset,
                pnl_class,
                trade.pnl
            ));
        }

        format!(
            r#"
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>方向</th>
                        <th>价格</th>
                        <th>数量</th>
                        <th>手续费</th>
                        <th>盈亏</th>
                    </tr>
                </thead>
                <tbody>
                    {}
                </tbody>
            </table>
            "#,
            rows
        )
    }

    /// 生成订单记录表格HTML
    fn generate_order_table(orders: &[BacktestOrder]) -> String {
        let mut rows = String::new();

        for order in orders {
            let side_class = match order.side {
                OrderSide::Buy => "buy",
                OrderSide::Sell => "sell",
            };

            // 根据订单状态设置行样式
            let status_class = match order.status {
                crate::types::OrderStatus::Filled => "status-filled",
                crate::types::OrderStatus::PartiallyFilled => "status-partial",
                crate::types::OrderStatus::Cancelled => "status-cancelled",
                crate::types::OrderStatus::Rejected => "status-rejected",
                _ => "status-other",
            };

            let price_str = order
                .price
                .map(|p| format!("{:.4}", p.value()))
                .unwrap_or_else(|| "市价".to_string());

            let filled_price_str = order
                .filled_price
                .map(|p| format!("{:.4}", p.value()))
                .unwrap_or_else(|| "-".to_string());

            let filled_time_str = order
                .filled_timestamp
                .map(|t| t.format("%Y-%m-%d %H:%M:%S").to_string())
                .unwrap_or_else(|| "-".to_string());

            // 订单状态显示文本
            let status_text = match order.status {
                crate::types::OrderStatus::Filled => "已成交",
                crate::types::OrderStatus::PartiallyFilled => "部分成交",
                crate::types::OrderStatus::Cancelled => "已取消",
                crate::types::OrderStatus::Rejected => "已拒绝",
                crate::types::OrderStatus::Pending => "待处理",
                crate::types::OrderStatus::Expired => "已过期",
            };

            rows.push_str(&format!(
                r#"
                <tr class="{}">
                    <td class="timestamp">{}</td>
                    <td class="timestamp">{}</td>
                    <td class="{}">{:?}</td>
                    <td class="{}">{}</td>
                    <td>{}</td>
                    <td>{:.4}</td>
                    <td>{:.4}</td>
                    <td>{}</td>
                    <td>{:.4} {}</td>
                </tr>
                "#,
                status_class,
                order.timestamp.format("%Y-%m-%d %H:%M:%S"),
                filled_time_str,
                side_class,
                order.side,
                status_class,
                status_text,
                price_str,
                order.quantity,
                order.filled_quantity,
                filled_price_str,
                order.fee,
                order.fee_asset
            ));
        }

        format!(
            r#"
            <table>
                <thead>
                    <tr>
                        <th>下单时间</th>
                        <th>成交时间</th>
                        <th>方向</th>
                        <th>状态</th>
                        <th>价格</th>
                        <th>数量</th>
                        <th>成交数量</th>
                        <th>成交价格</th>
                        <th>手续费</th>
                    </tr>
                </thead>
                <tbody>
                    {}
                </tbody>
            </table>
            "#,
            rows
        )
    }
    /// 推断价格精度与最小步长
    fn calc_price_precision_and_min_move_from_floats(values: &[f64]) -> (usize, String) {
        let mut max_decimals = 0usize;
        for &v in values {
            if !v.is_finite() {
                continue;
            }
            // 使用最多8位小数来估计原始小数位，并去掉末尾0
            let s = format!("{:.8}", v.abs());
            if let Some(dot) = s.find('.') {
                let frac = &s[dot + 1..];
                let trimmed = frac.trim_end_matches('0');
                let d = trimmed.len();
                if d > max_decimals {
                    max_decimals = d;
                }
            }
        }
        let min_move = if max_decimals == 0 {
            "1".to_string()
        } else {
            // 生成形如 0.001 的字符串
            let zeros = "0".repeat(max_decimals.saturating_sub(1));
            format!("0.{}1", zeros)
        };
        (max_decimals, min_move)
    }

    /// 从OHLC数据生成K线图数据
    fn generate_kline_data_from_ohlc(
        completed_ohlc: &[crate::types::OhlcData],
        current_ohlc: Option<&crate::types::OhlcData>,
        orders: &[BacktestOrder],
    ) -> String {
        // 生成TradingView格式的K线数据
        let mut candlestick_data = Vec::new();

        // 收集价格以推断精度
        let mut all_prices: Vec<f64> = Vec::new();

        // 添加已完成的OHLC数据
        for ohlc in completed_ohlc {
            all_prices.extend_from_slice(&[ohlc.open, ohlc.high, ohlc.low, ohlc.close]);
            candlestick_data.push(format!(
                "{{ time: {}, open: {:.4}, high: {:.4}, low: {:.4}, close: {:.4} }}",
                ohlc.timestamp.timestamp(),
                ohlc.open,
                ohlc.high,
                ohlc.low,
                ohlc.close
            ));
        }

        // 添加当前正在聚合的OHLC数据
        if let Some(current) = current_ohlc {
            all_prices.extend_from_slice(&[current.open, current.high, current.low, current.close]);
            candlestick_data.push(format!(
                "{{ time: {}, open: {:.4}, high: {:.4}, low: {:.4}, close: {:.4} }}",
                current.timestamp.timestamp(),
                current.open,
                current.high,
                current.low,
                current.close
            ));
        }

        // 生成交易标记 - 按状态分类
        let mut buy_markers = Vec::new();
        let mut sell_markers = Vec::new();
        let mut cancelled_markers = Vec::new();
        let mut rejected_markers: Vec<String> = Vec::new();

        for order in orders {
            let timestamp = order.timestamp.timestamp();

            // 根据订单状态决定显示方式
            match order.status {
                crate::types::OrderStatus::Filled => {
                    // 已成交订单：显示为实心箭头
                    if let Some(price) = order.filled_price {
                        let _price_value = price.value();
                        match order.side {
                            crate::types::OrderSide::Buy => {
                                buy_markers.push(format!(
                                    "{{ time: {}, position: 'belowBar', color: '#2196F3', shape: 'arrowUp', text: 'B' }}",
                                    timestamp
                                ));
                            }
                            crate::types::OrderSide::Sell => {
                                sell_markers.push(format!(
                                    "{{ time: {}, position: 'aboveBar', color: '#e91e63', shape: 'arrowDown', text: 'S' }}",
                                    timestamp
                                ));
                            }
                        }
                    }
                }
                crate::types::OrderStatus::PartiallyFilled => {
                    // 部分成交订单：显示为半透明箭头
                    if let Some(price) = order.filled_price {
                        let _price_value = price.value();
                        match order.side {
                            crate::types::OrderSide::Buy => {
                                buy_markers.push(format!(
                                    "{{ time: {}, position: 'belowBar', color: '#64B5F6', shape: 'arrowUp', text: 'PB' }}",
                                    timestamp
                                ));
                            }
                            crate::types::OrderSide::Sell => {
                                sell_markers.push(format!(
                                    "{{ time: {}, position: 'aboveBar', color: '#F48FB1', shape: 'arrowDown', text: 'PS' }}",
                                    timestamp
                                ));
                            }
                        }
                    }
                }
                crate::types::OrderStatus::Cancelled => {
                    // 被取消订单：显示为灰色箭头
                    match order.side {
                        crate::types::OrderSide::Buy => {
                            cancelled_markers.push(format!(
                                "{{ time: {}, position: 'belowBar', color: '#9E9E9E', shape: 'arrowUp', text: 'CB' }}",
                                timestamp
                            ));
                        }
                        crate::types::OrderSide::Sell => {
                            cancelled_markers.push(format!(
                                "{{ time: {}, position: 'aboveBar', color: '#9E9E9E', shape: 'arrowDown', text: 'CS' }}",
                                timestamp
                            ));
                        }
                    }
                }
                crate::types::OrderStatus::Rejected => {
                    // 被拒绝订单：显示为红色X
                    rejected_markers.push(format!(
                        "{{ time: {}, position: 'inBar', color: '#F44336', shape: 'circle', text: 'X' }}",
                        timestamp
                    ));
                }
                crate::types::OrderStatus::Pending => {
                    // Pending订单：显示为空心箭头
                    match order.side {
                        crate::types::OrderSide::Buy => {
                            buy_markers.push(format!(
                                "{{ time: {}, position: 'belowBar', color: '#64B5F6', shape: 'arrowUp', text: 'PB' }}",
                                timestamp
                            ));
                        }
                        crate::types::OrderSide::Sell => {
                            sell_markers.push(format!(
                                "{{ time: {}, position: 'aboveBar', color: '#F48FB1', shape: 'arrowDown', text: 'PS' }}",
                                timestamp
                            ));
                        }
                    }
                }
                _ => {
                    // 其他状态订单暂不显示
                }
            }
        }

        // 根据价格推断精度与最小步长
        let (price_precision, price_min_move) =
            Self::calc_price_precision_and_min_move_from_floats(&all_prices);

        format!(
            r#"
            const pricePrecision = {};
            const priceMinMove = {};
            const candlestickData = [{}];
            const buyMarkers = [{}];
            const sellMarkers = [{}];
            const cancelledMarkers = [{}];
            const rejectedMarkers = [{}];
            const allMarkersDefault = [...buyMarkers, ...sellMarkers, ...rejectedMarkers];
            const allMarkersWithCancelled = [...allMarkersDefault, ...cancelledMarkers];
            "#,
            price_precision,
            price_min_move,
            candlestick_data.join(",\n                "),
            buy_markers.join(",\n                "),
            sell_markers.join(",\n                "),
            cancelled_markers.join(",\n                "),
            rejected_markers.join(",\n                ")
        )
    }

    /// 生成K线图数据（从原始BBO数据）
    fn generate_kline_data(bbo_records: &[BacktestBbo], orders: &[BacktestOrder]) -> String {
        use std::collections::BTreeMap;

        // 1秒K线数据结构
        #[derive(Debug, Clone)]
        struct KlineBar {
            timestamp: DateTime<Utc>,
            open: f64,
            high: f64,
            low: f64,
            close: f64,
        }

        // 将BBO数据聚合成1秒K线
        let mut kline_map: BTreeMap<i64, Vec<f64>> = BTreeMap::new();

        for bbo in bbo_records {
            let mid_price = (bbo.bid_price.value() + bbo.ask_price.value()) / 2.0;
            // 将时间戳截断到秒级别
            let second_timestamp = bbo.timestamp.timestamp();
            kline_map
                .entry(second_timestamp)
                .or_insert_with(Vec::new)
                .push(mid_price);
        }

        // 生成K线数据
        let mut kline_bars = Vec::new();
        for (timestamp, prices) in kline_map {
            if !prices.is_empty() {
                let open = prices[0];
                let close = prices[prices.len() - 1];
                let high = prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
                let low = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));

                kline_bars.push(KlineBar {
                    timestamp: DateTime::from_timestamp(timestamp, 0).unwrap_or_default(),
                    open,
                    high,
                    low,
                    close,
                });
            }
        }

        // 生成TradingView格式的K线数据
        let mut candlestick_data = Vec::new();
        for bar in &kline_bars {
            candlestick_data.push(format!(
                "{{ time: {}, open: {:.4}, high: {:.4}, low: {:.4}, close: {:.4} }}",
                bar.timestamp.timestamp(),
                bar.open,
                bar.high,
                bar.low,
                bar.close
            ));
        }

        // 生成交易点位数据 - TradingView格式
        let mut buy_markers = Vec::new();
        let mut sell_markers = Vec::new();
        let mut cancelled_markers = Vec::new();
        let mut rejected_markers: Vec<String> = Vec::new();

        for order in orders {
            let timestamp = order.timestamp.timestamp();

            // 根据订单状态决定显示方式
            match order.status {
                crate::types::OrderStatus::Filled => {
                    // 已成交订单：显示为实心箭头
                    if let Some(price) = order.filled_price {
                        let _price_value = price.value();
                        match order.side {
                            OrderSide::Buy => {
                                buy_markers.push(format!(
                                    "{{ time: {}, position: 'belowBar', color: '#2196F3', shape: 'arrowUp', text: 'B' }}",
                                    timestamp
                                ));
                            }
                            OrderSide::Sell => {
                                sell_markers.push(format!(
                                    "{{ time: {}, position: 'aboveBar', color: '#e91e63', shape: 'arrowDown', text: 'S' }}",
                                    timestamp
                                ));
                            }
                        }
                    }
                }
                crate::types::OrderStatus::PartiallyFilled => {
                    // 部分成交订单：显示为半透明箭头
                    if let Some(price) = order.filled_price {
                        let _price_value = price.value();
                        match order.side {
                            OrderSide::Buy => {
                                buy_markers.push(format!(
                                    "{{ time: {}, position: 'belowBar', color: '#64B5F6', shape: 'arrowUp', text: 'PB' }}",
                                    timestamp
                                ));
                            }
                            OrderSide::Sell => {
                                sell_markers.push(format!(
                                    "{{ time: {}, position: 'aboveBar', color: '#F48FB1', shape: 'arrowDown', text: 'PS' }}",
                                    timestamp
                                ));
                            }
                        }
                    }
                }
                crate::types::OrderStatus::Cancelled => {
                    // 被取消订单：显示为灰色箭头
                    match order.side {
                        OrderSide::Buy => {
                            cancelled_markers.push(format!(
                                "{{ time: {}, position: 'belowBar', color: '#9E9E9E', shape: 'arrowUp', text: 'CB' }}",
                                timestamp
                            ));
                        }
                        OrderSide::Sell => {
                            cancelled_markers.push(format!(
                                "{{ time: {}, position: 'aboveBar', color: '#9E9E9E', shape: 'arrowDown', text: 'CS' }}",
                                timestamp
                            ));
                        }
                    }
                }
                crate::types::OrderStatus::Rejected => {
                    // 被拒绝订单：显示为红色X
                    rejected_markers.push(format!(
                        "{{ time: {}, position: 'inBar', color: '#F44336', shape: 'circle', text: 'X' }}",
                        timestamp
                    ));
                }
                _ => {
                    // 其他状态订单暂不显示
                }
            }
        }

        // 如果没有数据，提供默认值
        if candlestick_data.is_empty() {
            let now = Utc::now().timestamp();
            candlestick_data.push(format!(
                "{{ time: {}, open: 50000, high: 50000, low: 50000, close: 50000 }}",
                now
            ));
        }

        // 根据价格推断精度与最小步长
        let (price_precision, price_min_move) = Self::calc_price_precision_and_min_move_from_floats(
            &kline_bars
                .iter()
                .flat_map(|b| [b.open, b.high, b.low, b.close])
                .collect::<Vec<_>>(),
        );

        format!(
            r#"
            const pricePrecision = {};
            const priceMinMove = '{}';
            const candlestickData = [{}];
            const buyMarkers = [{}];
            const sellMarkers = [{}];
            const cancelledMarkers = [{}];
            const rejectedMarkers = [{}];
            const allMarkersDefault = [...buyMarkers, ...sellMarkers, ...rejectedMarkers];
            const allMarkersWithCancelled = [...allMarkersDefault, ...cancelledMarkers];
            "#,
            price_precision,
            price_min_move,
            candlestick_data.join(",\n                "),
            buy_markers.join(",\n                "),
            sell_markers.join(",\n                "),
            cancelled_markers.join(",\n                "),
            rejected_markers.join(",\n                ")
        )
    }
}

/// CSV导出器
pub struct CsvExporter;

impl CsvExporter {
    /// 导出订单记录到CSV文件
    pub async fn export_orders_to_csv(
        orders: &[BacktestOrder],
        filename: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use csv::Writer;

        let mut wtr = Writer::from_path(filename)?;

        // 写入CSV头部
        wtr.write_record(&[
            "订单ID",
            "客户端订单ID",
            "交易对",
            "订单类型",
            "订单方向",
            "订单价格",
            "订单数量",
            "订单状态",
            "下单时间",
            "成交时间",
            "成交价格",
            "成交数量",
            "手续费",
            "手续费币种",
        ])?;

        // 写入订单数据
        for order in orders {
            let order_type_str = match order.order_type {
                crate::types::OrderType::Market => "市价单",
                crate::types::OrderType::Limit => "限价单",
                crate::types::OrderType::LimitIOC => "IOC限价单",
                crate::types::OrderType::LimitGTX => "GTX限价单",
            };

            let side_str = match order.side {
                crate::types::OrderSide::Buy => "买入",
                crate::types::OrderSide::Sell => "卖出",
            };

            let status_str = match order.status {
                crate::types::OrderStatus::Pending => "待处理",
                crate::types::OrderStatus::PartiallyFilled => "部分成交",
                crate::types::OrderStatus::Filled => "已成交",
                crate::types::OrderStatus::Cancelled => "已取消",
                crate::types::OrderStatus::Expired => "已过期",
                crate::types::OrderStatus::Rejected => "已拒绝",
            };

            let price_str = order
                .price
                .map(|p| format!("{:.8}", p.value()))
                .unwrap_or_else(|| "市价".to_string());

            let filled_price_str = order
                .filled_price
                .map(|p| format!("{:.8}", p.value()))
                .unwrap_or_else(|| "-".to_string());

            let filled_time_str = order
                .filled_timestamp
                .map(|t| t.format("%Y-%m-%d %H:%M:%S").to_string())
                .unwrap_or_else(|| "-".to_string());

            wtr.write_record(&[
                &order.order_id,
                &order.client_order_id,
                &order.symbol,
                order_type_str,
                side_str,
                &price_str,
                &format!("{:.8}", order.quantity),
                status_str,
                &order.timestamp.format("%Y-%m-%d %H:%M:%S").to_string(),
                &filled_time_str,
                &filled_price_str,
                &format!("{:.8}", order.filled_quantity),
                &format!("{:.8}", order.fee),
                &order.fee_asset,
            ])?;
        }

        wtr.flush()?;
        tracing::info!("订单记录已导出到CSV文件: {}", filename);
        Ok(())
    }

    /// 导出交易记录到CSV文件
    pub async fn export_trades_to_csv(
        trades: &[BacktestTrade],
        filename: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use csv::Writer;

        let mut wtr = Writer::from_path(filename)?;

        // 写入CSV头部
        wtr.write_record(&[
            "交易ID",
            "订单ID",
            "交易对",
            "交易方向",
            "成交价格",
            "成交数量",
            "手续费",
            "手续费币种",
            "交易时间",
            "是否为Maker",
            "盈亏",
        ])?;

        // 写入交易数据
        for trade in trades {
            let side_str = match trade.side {
                crate::types::OrderSide::Buy => "买入",
                crate::types::OrderSide::Sell => "卖出",
            };

            let maker_str = if trade.is_maker { "是" } else { "否" };

            wtr.write_record(&[
                &trade.trade_id,
                &trade.order_id,
                &trade.symbol,
                side_str,
                &format!("{:.8}", trade.price.value()),
                &format!("{:.8}", trade.quantity),
                &format!("{:.8}", trade.fee),
                &trade.fee_asset,
                &trade.timestamp.format("%Y-%m-%d %H:%M:%S").to_string(),
                maker_str,
                &format!("{:.8}", trade.pnl),
            ])?;
        }

        wtr.flush()?;
        tracing::info!("交易记录已导出到CSV文件: {}", filename);
        Ok(())
    }

    /// 导出所有回测数据到CSV文件
    pub async fn export_all_to_csv(
        recorder: &Arc<Mutex<crate::types::BacktestRecorder>>,
        base_filename: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let recorder_guard = recorder.lock().await;

        // 生成带时间戳的文件名
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let orders_filename = format!("{}_orders_{}.csv", base_filename, timestamp);
        let trades_filename = format!("{}_trades_{}.csv", base_filename, timestamp);

        // 导出订单记录
        Self::export_orders_to_csv(&recorder_guard.orders, &orders_filename).await?;

        // 导出交易记录
        Self::export_trades_to_csv(&recorder_guard.trades, &trades_filename).await?;

        tracing::info!("所有回测数据已导出到CSV文件:");
        tracing::info!("  订单记录: {}", orders_filename);
        tracing::info!("  交易记录: {}", trades_filename);

        Ok(())
    }

    /// 导出回测总结数据到CSV文件
    pub async fn export_summary_to_csv(
        summary: &BacktestSummary,
        base_filename: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let orders_filename = format!("{}_orders_{}.csv", base_filename, timestamp);
        let trades_filename = format!("{}_trades_{}.csv", base_filename, timestamp);

        // 导出订单记录
        Self::export_orders_to_csv(&summary.orders, &orders_filename).await?;

        // 导出交易记录
        Self::export_trades_to_csv(&summary.trades, &trades_filename).await?;

        tracing::info!("回测总结数据已导出到CSV文件:");
        tracing::info!("  订单记录: {}", orders_filename);
        tracing::info!("  交易记录: {}", trades_filename);

        Ok(())
    }
}
