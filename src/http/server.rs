use crate::config::ConfigManager;
use crate::http::routes;
use crate::Result;
use std::net::SocketAddr;
use tracing::{debug, info, warn};

/// HTTP服务器
pub struct HttpServer {
    port: u16,
}

impl HttpServer {
    /// 创建新的HTTP服务器
    pub fn new() -> Result<Self> {
        let config = ConfigManager::get()?;
        Ok(Self {
            port: config.http_port,
        })
    }

    /// 使用指定端口创建HTTP服务器
    pub fn with_port(port: u16) -> Self {
        Self { port }
    }

    /// 启动HTTP服务器
    pub async fn start(&self) -> Result<()> {
        let config = ConfigManager::get()?;
        let addr = SocketAddr::from(([0, 0, 0, 0], self.port));

        // 创建路由
        let all_routes = routes::create_routes();

        // 检查是否启用TLS
        if config.http_tls.enabled {
            match &config.http_tls.cert_source {
                Some(crate::config::TlsCertSource::Files {
                    cert_path,
                    key_path,
                }) => {
                    debug!(
                        "🚀 Starting HTTPS server on: {} with certificate files",
                        addr
                    );
                    debug!("📁 Certificate path: {}", cert_path.display());
                    debug!("🔑 Key path: {}", key_path.display());
                    debug!("🔒 TLS enabled, waiting for connections...");

                    // 使用文件路径启动HTTPS服务器
                    warp::serve(all_routes)
                        .tls()
                        .cert_path(cert_path)
                        .key_path(key_path)
                        .run(addr)
                        .await;
                }
                Some(crate::config::TlsCertSource::SelfSigned { subject: _ }) => {
                    warn!("Self-signed certificates are not directly supported by warp. Please use certificate files instead.");
                    warn!("Falling back to HTTP server on: {}", addr);

                    // 回退到HTTP服务器
                    warp::serve(all_routes).run(addr).await;
                }
                None => {
                    warn!(
                        "TLS enabled but no certificate source configured. Falling back to HTTP."
                    );
                    warp::serve(all_routes).run(addr).await;
                }
            }
        } else {
            info!("Starting HTTP server on: {}", addr);

            // 启动HTTP服务器
            warp::serve(all_routes).run(addr).await;
        }

        Ok(())
    }

    /// 获取服务器端口
    pub fn port(&self) -> u16 {
        self.port
    }
}

impl Default for HttpServer {
    fn default() -> Self {
        Self::with_port(8081)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_http_server_creation() {
        let server = HttpServer::with_port(8888);
        assert_eq!(server.port(), 8888);
    }

    #[tokio::test]
    async fn test_http_server_routes() {
        // 这个测试验证服务器可以创建路由
        let _server = HttpServer::with_port(0);
        let _routes = routes::create_routes();
        // 在实际测试中，你可能需要更复杂的集成测试
    }
}
