use crate::config::{TlsCertSource, TlsConfig};
use crate::{BacktestError, Result};
use rcgen::{Certificate, CertificateParams, DistinguishedName, DnType};
use rustls::{Certificate as RustlsCertificate, PrivateKey, ServerConfig};
use rustls_pemfile::{certs, pkcs8_private_keys, rsa_private_keys};
use std::fs::File;
use std::io::BufReader;
use std::path::Path;
use std::sync::Arc;
use time::{Duration, OffsetDateTime};
use tracing::{debug, info, warn};

/// TLS配置构建器
pub struct TlsConfigBuilder;

impl TlsConfigBuilder {
    /// 根据TLS配置创建rustls ServerConfig
    pub async fn build_server_config(tls_config: &TlsConfig) -> Result<Option<Arc<ServerConfig>>> {
        if !tls_config.enabled {
            return Ok(None);
        }

        let cert_source = tls_config.cert_source.as_ref().ok_or_else(|| {
            BacktestError::Config("TLS enabled but no certificate source specified".to_string())
        })?;

        let (cert_chain, private_key) = match cert_source {
            TlsCertSource::Files {
                cert_path,
                key_path,
            } => {
                debug!("Loading TLS certificates from files");
                Self::load_certs_from_files(cert_path, key_path).await?
            }
            TlsCertSource::SelfSigned { subject } => {
                debug!("Generating self-signed TLS certificate");
                let subject_name = subject.as_deref().unwrap_or("localhost");
                Self::generate_self_signed_cert(subject_name).await?
            }
        };

        let config = ServerConfig::builder()
            .with_safe_defaults()
            .with_no_client_auth()
            .with_single_cert(cert_chain, private_key)
            .map_err(|e| BacktestError::Config(format!("Failed to create TLS config: {}", e)))?;

        Ok(Some(Arc::new(config)))
    }

    /// 从文件加载证书和私钥
    async fn load_certs_from_files(
        cert_path: &Path,
        key_path: &Path,
    ) -> Result<(Vec<RustlsCertificate>, PrivateKey)> {
        // 加载证书链
        let cert_file = File::open(cert_path).map_err(|e| {
            BacktestError::Config(format!("Failed to open cert file {:?}: {}", cert_path, e))
        })?;
        let mut cert_reader = BufReader::new(cert_file);
        let cert_chain = certs(&mut cert_reader)
            .map_err(|e| BacktestError::Config(format!("Failed to parse certificates: {}", e)))?
            .into_iter()
            .map(RustlsCertificate)
            .collect();

        // 加载私钥
        let key_file = File::open(key_path).map_err(|e| {
            BacktestError::Config(format!("Failed to open key file {:?}: {}", key_path, e))
        })?;
        let mut key_reader = BufReader::new(key_file);

        // 首先尝试PKCS#8格式
        let mut keys = pkcs8_private_keys(&mut key_reader).map_err(|e| {
            BacktestError::Config(format!("Failed to parse PKCS#8 private key: {}", e))
        })?;

        // 如果PKCS#8格式失败，尝试RSA格式
        if keys.is_empty() {
            let key_file = File::open(key_path).map_err(|e| {
                BacktestError::Config(format!("Failed to reopen key file {:?}: {}", key_path, e))
            })?;
            let mut key_reader = BufReader::new(key_file);
            keys = rsa_private_keys(&mut key_reader).map_err(|e| {
                BacktestError::Config(format!("Failed to parse RSA private key: {}", e))
            })?;
        }

        if keys.is_empty() {
            return Err(BacktestError::Config(
                "No private keys found in key file (tried both PKCS#8 and RSA formats)".to_string(),
            ));
        }

        let private_key = PrivateKey(keys.remove(0));

        Ok((cert_chain, private_key))
    }

    /// 生成自签名证书
    async fn generate_self_signed_cert(
        subject_name: &str,
    ) -> Result<(Vec<RustlsCertificate>, PrivateKey)> {
        warn!(
            "Generating self-signed certificate for '{}' - NOT suitable for production!",
            subject_name
        );

        let mut params = CertificateParams::new(vec![subject_name.to_string()]);

        // 设置证书参数
        params.not_before = OffsetDateTime::now_utc() - Duration::days(1);
        params.not_after = OffsetDateTime::now_utc() + Duration::days(365);

        // 设置主题名称
        let mut distinguished_name = DistinguishedName::new();
        distinguished_name.push(DnType::CommonName, subject_name);
        params.distinguished_name = distinguished_name;

        // 生成证书
        let cert = Certificate::from_params(params)
            .map_err(|e| BacktestError::Config(format!("Failed to generate certificate: {}", e)))?;

        // 转换为rustls格式
        let cert_der = cert.serialize_der().map_err(|e| {
            BacktestError::Config(format!("Failed to serialize certificate: {}", e))
        })?;
        let key_der = cert.serialize_private_key_der();

        let cert_chain = vec![RustlsCertificate(cert_der)];
        let private_key = PrivateKey(key_der);

        info!(
            "Self-signed certificate generated successfully for '{}'",
            subject_name
        );

        Ok((cert_chain, private_key))
    }
}

/// TLS工具函数
pub struct TlsUtils;

impl TlsUtils {
    /// 检查TLS配置是否有效
    pub fn validate_config(tls_config: &TlsConfig) -> Result<()> {
        if !tls_config.enabled {
            return Ok(());
        }

        match &tls_config.cert_source {
            Some(TlsCertSource::Files {
                cert_path,
                key_path,
            }) => {
                if !cert_path.exists() {
                    return Err(BacktestError::Config(format!(
                        "Certificate file does not exist: {:?}",
                        cert_path
                    )));
                }
                if !key_path.exists() {
                    return Err(BacktestError::Config(format!(
                        "Private key file does not exist: {:?}",
                        key_path
                    )));
                }
            }
            Some(TlsCertSource::SelfSigned { .. }) => {
                // 自签名证书无需验证文件存在性
            }
            None => {
                return Err(BacktestError::Config(
                    "TLS enabled but no certificate source specified".to_string(),
                ));
            }
        }

        Ok(())
    }

    /// 获取TLS配置的描述信息
    pub fn get_config_description(tls_config: &TlsConfig) -> String {
        if !tls_config.enabled {
            return "TLS disabled".to_string();
        }

        match &tls_config.cert_source {
            Some(TlsCertSource::Files {
                cert_path,
                key_path,
            }) => {
                format!(
                    "TLS enabled with certificate files: {:?}, {:?}",
                    cert_path, key_path
                )
            }
            Some(TlsCertSource::SelfSigned { subject }) => {
                let subject_name = subject.as_deref().unwrap_or("localhost");
                format!(
                    "TLS enabled with self-signed certificate for '{}'",
                    subject_name
                )
            }
            None => "TLS enabled but no certificate source configured".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_self_signed_cert_generation() {
        let (cert_chain, _private_key) =
            TlsConfigBuilder::generate_self_signed_cert("test.localhost")
                .await
                .expect("Failed to generate self-signed certificate");

        assert!(!cert_chain.is_empty());
    }

    #[test]
    fn test_tls_config_validation() {
        // 测试禁用TLS的配置
        let disabled_config = TlsConfig::default();
        assert!(TlsUtils::validate_config(&disabled_config).is_ok());

        // 测试自签名证书配置
        let self_signed_config = TlsConfig::with_self_signed(Some("test.localhost".to_string()));
        assert!(TlsUtils::validate_config(&self_signed_config).is_ok());

        // 测试文件证书配置（文件不存在）
        let file_config = TlsConfig::with_files(
            PathBuf::from("nonexistent.crt"),
            PathBuf::from("nonexistent.key"),
        );
        assert!(TlsUtils::validate_config(&file_config).is_err());
    }

    #[test]
    fn test_config_description() {
        let disabled_config = TlsConfig::default();
        assert_eq!(
            TlsUtils::get_config_description(&disabled_config),
            "TLS disabled"
        );

        let self_signed_config = TlsConfig::with_self_signed(Some("test.localhost".to_string()));
        assert!(TlsUtils::get_config_description(&self_signed_config).contains("self-signed"));
    }
}
