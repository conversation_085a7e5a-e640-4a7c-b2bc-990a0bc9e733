//! Account management module for the backtest framework
//! 
//! This module provides comprehensive account management functionality including:
//! - Account state tracking (balance, positions, margin)
//! - Position management (long/short positions, average price calculation)
//! - Risk management (leverage calculation, margin requirements)
//! - Trade execution tracking and P&L calculation

pub mod account;
pub mod balance;
pub mod manager;
pub mod position;
pub mod types;

pub use account::Account;
pub use balance::Balance;
pub use manager::AccountManager;
pub use position::Position;
pub use types::*;
