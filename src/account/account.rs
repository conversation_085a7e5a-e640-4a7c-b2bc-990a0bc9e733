use crate::account::balance::BalanceManager;
use crate::account::position::{Position, PositionSummary};
use crate::account::types::{AccountConfig, AccountStats, AccountType, RiskMetrics, TradeRecord};
use crate::types::{OrderSide, Price};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 账户主体结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Account {
    /// 账户ID
    pub account_id: String,
    /// 账户配置
    pub config: AccountConfig,
    /// 余额管理器
    pub balance_manager: BalanceManager,
    /// 仓位管理（按交易对）
    pub positions: HashMap<String, Position>,
    /// 交易记录
    pub trade_history: Vec<TradeRecord>,
    /// 账户统计信息
    pub stats: AccountStats,
    /// 风险指标
    pub risk_metrics: RiskMetrics,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl Account {
    /// 创建新账户
    pub fn new(account_id: String, config: AccountConfig) -> Self {
        let mut balance_manager = BalanceManager::new();

        // 初始化USDT余额
        balance_manager.initialize_balance("USDT".to_string(), config.initial_balance);

        let mut account = Self {
            account_id,
            config,
            balance_manager,
            positions: HashMap::new(),
            trade_history: Vec::new(),
            stats: AccountStats::default(),
            risk_metrics: RiskMetrics::default(),
            created_at: Utc::now(),
            last_updated: Utc::now(),
        };

        // 初始化统计信息
        account.update_stats(&HashMap::new());
        account
    }

    /// 获取或创建仓位（统一使用大写symbol，避免大小写不一致导致的数据错配）
    pub fn get_or_create_position(&mut self, symbol: &str) -> &mut Position {
        let key = symbol.to_uppercase();
        self.positions
            .entry(key.clone())
            .or_insert_with(|| Position::new(key, self.config.max_leverage))
    }

    /// 处理交易
    pub fn process_trade(
        &mut self,
        trade: TradeRecord,
        current_prices: &HashMap<String, Price>,
    ) -> Result<(), String> {
        // 验证交易对是否支持
        if !self.config.supported_symbols.contains(&trade.symbol) {
            return Err(format!("Unsupported symbol: {}", trade.symbol));
        }

        // 获取当前价格
        let current_price = current_prices
            .get(&trade.symbol)
            .copied()
            .unwrap_or(trade.price);

        // 处理手续费（支持负费率返佣）
        if trade.fee > 0.0 {
            // 正手续费：扣减余额
            self.balance_manager
                .subtract_balance(&trade.fee_asset, trade.fee)?;
        } else if trade.fee < 0.0 {
            // 负手续费：按返佣加回余额
            self.balance_manager
                .add_balance(&trade.fee_asset, -trade.fee);
        } // fee == 0.0 则不处理

        // 期货交易：不直接扣除余额，而是处理保证金
        let notional_value = trade.notional_value();
        let max_leverage = self.config.max_leverage; // 先获取杠杆值
        let required_margin = notional_value / max_leverage; // 计算所需保证金

        // 检查是否有足够的可用余额作为保证金
        let available_balance = self.balance_manager.get_available_balance("USDT");
        if available_balance < required_margin {
            return Err("Insufficient margin".to_string());
        }

        // 获取当前仓位信息来判断是否需要冻结保证金
        let current_position_qty = self
            .positions
            .get(&trade.symbol)
            .map(|p| p.quantity)
            .unwrap_or(0.0);

        // 冻结保证金（如果是开仓或增加仓位）
        if (trade.side == OrderSide::Buy && current_position_qty >= 0.0)
            || (trade.side == OrderSide::Sell && current_position_qty <= 0.0)
        {
            // 开仓或增加仓位：冻结额外保证金
            self.balance_manager
                .freeze_balance("USDT", required_margin)?;
        }

        // 更新仓位
        let position = self.get_or_create_position(&trade.symbol);
        let realized_pnl = position.process_trade(&trade, current_price)?;

        // 如果有已实现盈亏，更新USDT余额
        if realized_pnl != 0.0 {
            if realized_pnl > 0.0 {
                self.balance_manager.add_balance("USDT", realized_pnl);
            } else {
                self.balance_manager
                    .subtract_balance("USDT", -realized_pnl)?;
            }
        }

        // 记录交易
        let trade_ts = trade.timestamp;
        self.trade_history.push(trade);

        // 更新统计信息
        self.update_stats(current_prices);
        // 更新风险指标
        self.update_risk_metrics();
        self.last_updated = trade_ts;

        Ok(())
    }

    /// 更新账户统计信息
    pub fn update_stats(&mut self, current_prices: &HashMap<String, Price>) {
        // 计算总资产价值
        let mut total_value = self.balance_manager.get_total_balance("USDT");
        let mut used_margin = 0.0;
        let mut unrealized_pnl = 0.0;
        let mut position_count = 0;

        // 遍历所有仓位
        for position in self.positions.values_mut() {
            if !position.is_empty() {
                // 即使当前价格不存在，也用均价作为临时价格以便统计持仓数量，避免因为价格缓存未更新而导致仓位/盈亏不显示
                let current_price = current_prices
                    .get(&position.symbol)
                    .copied()
                    .unwrap_or(position.avg_price);

                position.update_unrealized_pnl(current_price);
                position.update_margin(current_price);

                unrealized_pnl += position.unrealized_pnl;
                used_margin += position.margin;
                position_count += 1;
            }
        }

        total_value += unrealized_pnl;

        // 计算已实现盈亏
        let realized_pnl: f64 = self.positions.values().map(|p| p.realized_pnl).sum();

        // 计算当前杠杆
        let available_balance = self.balance_manager.get_available_balance("USDT");
        let current_leverage = if available_balance > 0.0 {
            used_margin / available_balance
        } else {
            0.0
        };

        // 计算保证金率
        let margin_ratio = if used_margin > 0.0 {
            (available_balance + unrealized_pnl) / used_margin
        } else {
            f64::INFINITY
        };

        // 计算总手续费
        let total_fees: f64 = self.trade_history.iter().map(|t| t.fee).sum();

        self.stats = AccountStats {
            total_value,
            available_balance,
            used_margin,
            unrealized_pnl,
            realized_pnl,
            current_leverage,
            margin_ratio,
            position_count,
            total_trades: self.trade_history.len(),
            total_fees,
            last_updated: Utc::now(),
        };
    }

    /// 更新风险指标
    pub fn update_risk_metrics(&mut self) {
        use std::cmp::Ordering;

        if self.trade_history.is_empty() {
            return;
        }

        // 1) 准备：按时间排序交易记录（不改变原始顺序）
        let mut trades = self.trade_history.clone();
        trades.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        // 2) 使用逐笔仓位与FIFO批次模拟，精确计算每次"发生平仓的交易"的净盈亏（包含分摊手续费）
        #[derive(Clone, Debug)]
        struct Lot {
            qty: f64,
            price: f64,
            fee: f64,
        }
        #[derive(Clone, Debug, Default)]
        struct SimPos {
            side: i8,
            lots: Vec<Lot>,
        } // side: 1=Long, -1=Short, 0=None

        let mut sim_positions: HashMap<String, SimPos> = HashMap::new();

        let mut equity = self.config.initial_balance;
        let mut equity_curve: Vec<f64> = Vec::with_capacity(trades.len() + 1);
        equity_curve.push(equity);

        // 每笔交易的收益率（用于Sharpe与VaR），按净值变化/事前净值计算
        let mut trade_returns: Vec<f64> = Vec::with_capacity(trades.len());
        // 仅统计发生了"平仓"的交易的净盈亏（用于胜率、盈亏比、最大单笔）
        let mut closing_trade_pnls: Vec<f64> = Vec::new();

        for tr in trades.iter() {
            let dir: i8 = match tr.side {
                OrderSide::Buy => 1,
                OrderSide::Sell => -1,
            };
            let entry_price = tr.price.value();
            let qty = tr.quantity.max(0.0);
            if qty <= 0.0 {
                continue;
            }

            let pos = sim_positions
                .entry(tr.symbol.clone())
                .or_insert_with(SimPos::default);

            let mut net_change_this_trade = 0.0_f64; // 影响权益的净变化（包含所有手续费）
            let mut closing_net_this_trade = 0.0_f64; // 仅平仓部分的净盈亏（分摊进出场手续费）

            match pos.side.cmp(&dir) {
                // 同向：开仓/加仓（记录批次，全部手续费计入开仓成本）
                Ordering::Equal | Ordering::Less
                    if pos.side != 0 && pos.side == dir || pos.side == 0 =>
                {
                    pos.side = dir;
                    pos.lots.push(Lot {
                        qty,
                        price: entry_price,
                        fee: tr.fee,
                    });
                    // 仅开仓：权益净变动为 -手续费
                    net_change_this_trade -= tr.fee;
                }
                // 反向：可能发生平仓与反手开仓
                _ => {
                    // 计算当前持仓总数量（按批次求和）
                    let open_qty: f64 = pos.lots.iter().map(|l| l.qty).sum();
                    if open_qty <= 0.0 {
                        // 理论上不会发生，但做防御
                        pos.side = dir;
                        pos.lots.clear();
                        pos.lots.push(Lot {
                            qty,
                            price: entry_price,
                            fee: tr.fee,
                        });
                        net_change_this_trade -= tr.fee;
                    } else {
                        // 先确定本次交易中用于平仓的数量
                        let closing_total = qty.min(open_qty);
                        let opening_total = (qty - closing_total).max(0.0);

                        // 将本次手续费在“平仓部分”和“新开部分”之间按数量占比分摊
                        let closing_fee_total = if qty > 0.0 {
                            tr.fee * (closing_total / qty)
                        } else {
                            0.0
                        };
                        let opening_fee_total = tr.fee - closing_fee_total;

                        // 平仓：从FIFO批次逐步扣减，计算净盈亏
                        let mut remaining_to_close = closing_total;
                        let mut fee_left_for_closing = closing_fee_total; // 用于在各批次之间继续按比例分摊

                        // 为了按比例分摊平仓手续费，需要先保存remaining_to_close初始值
                        let mut closing_total_left = closing_total;

                        while remaining_to_close > 1e-12 && !pos.lots.is_empty() {
                            let mut lot = pos.lots.remove(0);
                            let take_qty = remaining_to_close.min(lot.qty);
                            let portion = take_qty / lot.qty;

                            // 入场手续费按数量比例分摊到已平仓部分
                            let entry_fee_alloc = lot.fee * portion;
                            lot.fee -= entry_fee_alloc;

                            // 平仓手续费按本次平仓数量比例再分摊
                            let closing_fee_alloc = if closing_total_left > 0.0 {
                                let p = take_qty / closing_total_left;
                                let alloc = fee_left_for_closing * p;
                                fee_left_for_closing -= alloc;
                                closing_total_left -= take_qty;
                                alloc
                            } else {
                                0.0
                            };

                            // 价差收益（按方向）
                            let pnl_gross = if pos.side == 1 {
                                // 多头被卖出
                                (entry_price - lot.price) * take_qty
                            } else {
                                // 空头被买入
                                (lot.price - entry_price) * take_qty
                            };

                            let pnl_net = pnl_gross - entry_fee_alloc - closing_fee_alloc;
                            closing_net_this_trade += pnl_net;

                            // 扣减批次剩余数量
                            lot.qty -= take_qty;
                            if lot.qty > 1e-12 {
                                // 批次未完全用尽，放回队列前端以保持FIFO
                                pos.lots.insert(0, lot);
                            }

                            remaining_to_close -= take_qty;
                        }

                        // 本次交易对权益的净影响 = 平仓净盈亏 - 新开部分的手续费
                        net_change_this_trade += closing_net_this_trade;
                        net_change_this_trade -= opening_fee_total;

                        // 若发生反手开仓：建立新方向批次，并更新方向
                        if opening_total > 1e-12 {
                            pos.side = dir;
                            pos.lots.clear(); // 旧方向应已用尽
                            pos.lots.push(Lot {
                                qty: opening_total,
                                price: entry_price,
                                fee: opening_fee_total,
                            });
                        } else {
                            // 可能完全平掉
                            if pos.lots.is_empty() {
                                pos.side = 0;
                            }
                        }

                        // 记录仅针对发生了平仓的交易的净盈亏（用于胜率/盈亏比统计）
                        if closing_total > 1e-12 {
                            closing_trade_pnls.push(closing_net_this_trade);
                        }
                    }
                }
            }

            // 记录收益率并更新权益曲线
            let prev_equity = equity;
            if prev_equity > 0.0 {
                trade_returns.push(net_change_this_trade / prev_equity);
            }
            equity += net_change_this_trade;
            equity_curve.push(equity);
        }

        // 3) 计算各项指标
        // 胜率
        let wins = closing_trade_pnls.iter().filter(|&&x| x > 0.0).count();
        let total_closed_trades = closing_trade_pnls.len();
        tracing::debug!(
            "wins: {}, total_closed_trades: {}",
            wins,
            total_closed_trades
        );
        let win_rate = if total_closed_trades > 0 {
            (wins as f64 / total_closed_trades as f64) * 100.0
        } else {
            0.0
        };

        // 盈亏比、最大单笔
        let profits: Vec<f64> = closing_trade_pnls
            .iter()
            .cloned()
            .filter(|&x| x > 0.0)
            .collect();
        let losses: Vec<f64> = closing_trade_pnls
            .iter()
            .cloned()
            .filter(|&x| x < 0.0)
            .map(|x| -x)
            .collect();

        let avg_profit = if !profits.is_empty() {
            profits.iter().sum::<f64>() / profits.len() as f64
        } else {
            0.0
        };
        let avg_loss = if !losses.is_empty() {
            losses.iter().sum::<f64>() / losses.len() as f64
        } else {
            0.0
        };
        let profit_loss_ratio = if avg_loss > 0.0 {
            avg_profit / avg_loss
        } else {
            0.0
        };

        let max_profit = profits.iter().fold(0.0_f64, |a, &b| a.max(b));
        let max_loss = losses.iter().fold(0.0_f64, |a, &b| a.max(b));

        // 最大回撤（基于逐笔交易的净值曲线）
        let mut max_drawdown = 0.0_f64;
        let mut peak = equity_curve[0];
        for &v in equity_curve.iter().skip(1) {
            if v > peak {
                peak = v;
            }
            if peak > 0.0 {
                let dd = (peak - v) / peak * 100.0;
                if dd > max_drawdown {
                    max_drawdown = dd;
                }
            }
        }

        // 区间/年化收益率
        let equity_start = equity_curve.first().copied().unwrap_or(0.0);
        let equity_end = equity_curve.last().copied().unwrap_or(equity_start);
        let first_ts = trades.first().unwrap().timestamp;
        let last_ts = trades.last().unwrap().timestamp;
        let days = (last_ts - first_ts).num_seconds() as f64 / 86_400.0;
        let r = if equity_start > 0.0 {
            (equity_end / equity_start) - 1.0
        } else {
            0.0
        };
        let interval_return = r * 100.0;
        // 估算年化（对任意跨度按CAGR估算，并限制到±1000%防极端）
        let annual_return_estimate = if equity_start > 0.0 && days > 0.0 {
            let est = ((1.0 + r).powf(365.0 / days) - 1.0) * 100.0;
            est.clamp(-1000.0, 1000.0)
        } else {
            0.0
        };
        // 兼容字段：当跨度>=1天，采用严格CAGR；否则回退为区间收益率
        let annual_return = if days >= 1.0 {
            annual_return_estimate
        } else {
            interval_return
        };

        // Sharpe（基于逐笔收益率，按交易频率年化）
        let sharpe_ratio = if trade_returns.len() >= 2 {
            let n = trade_returns.len() as f64;
            let mean = trade_returns.iter().sum::<f64>() / n;
            let var = {
                let mut acc = 0.0;
                for r in &trade_returns {
                    acc += (r - mean) * (r - mean);
                }
                // 使用样本方差（n-1），若n==1上面已排除
                acc / (n - 1.0)
            };
            let std = var.max(0.0).sqrt();
            if std > 0.0 {
                // 交易频率年化：以首尾时间跨度估算每年交易次数
                let trades_per_year = if days > 0.0 {
                    (trades.len() as f64) * (365.0 / days)
                } else {
                    0.0
                };
                if trades_per_year > 0.0 {
                    mean / std * trades_per_year.sqrt()
                } else {
                    0.0
                }
            } else {
                0.0
            }
        } else {
            0.0
        };

        // 历史法VaR 95%（逐笔收益率序列的5%分位，转为正的损失百分比）
        let var_95 = if !trade_returns.is_empty() {
            let mut r = trade_returns.clone();
            r.sort_by(|a, b| a.partial_cmp(b).unwrap_or(Ordering::Equal));
            let idx = ((r.len() as f64) * 0.05).floor() as usize;
            let q5 = *r.get(idx.min(r.len() - 1)).unwrap_or(&0.0);
            (-q5.max(0.0)) * 100.0 // 输出为%损失
        } else {
            0.0
        };

        self.risk_metrics = RiskMetrics {
            max_drawdown,
            sharpe_ratio,
            win_rate,
            annual_return,
            interval_return,
            annual_return_estimate,
            profit_loss_ratio,
            var_95,
            max_loss,
            max_profit,
        };
    }

    /// 获取账户摘要
    pub fn get_summary(&self, current_prices: &HashMap<String, Price>) -> AccountSummary {
        let position_summaries: Vec<PositionSummary> = self
            .positions
            .values()
            .filter(|p| !p.is_empty())
            .map(|p| {
                let current_price = current_prices
                    .get(&p.symbol)
                    .copied()
                    .unwrap_or(p.avg_price);
                p.get_summary(current_price)
            })
            .collect();

        AccountSummary {
            account_id: self.account_id.clone(),
            account_type: self.config.account_type.clone(),
            stats: self.stats.clone(),
            positions: position_summaries,
            balances: self.balance_manager.get_non_zero_balances(),
            risk_metrics: self.risk_metrics.clone(),
            last_updated: self.last_updated,
        }
    }

    /// 检查是否有足够资金进行交易
    pub fn can_afford_trade(
        &self,
        symbol: &str,
        side: OrderSide,
        price: Price,
        quantity: f64,
    ) -> bool {
        match side {
            OrderSide::Buy => {
                let cost = price.value() * quantity;
                let fee = cost * self.config.taker_fee_rate;
                let total_cost = cost + fee;
                self.balance_manager
                    .has_sufficient_balance("USDT", total_cost)
            }
            OrderSide::Sell => {
                // 对于卖出，需要检查是否有足够的仓位
                if let Some(position) = self.positions.get(symbol) {
                    position.quantity >= quantity
                } else {
                    false
                }
            }
        }
    }

    /// 计算最大可交易数量
    pub fn calculate_max_trade_quantity(&self, symbol: &str, side: OrderSide, price: Price) -> f64 {
        match side {
            OrderSide::Buy => {
                let available_balance = self.balance_manager.get_available_balance("USDT");
                let unit_cost = price.value() * (1.0 + self.config.taker_fee_rate);
                if unit_cost > 0.0 {
                    available_balance / unit_cost
                } else {
                    0.0
                }
            }
            OrderSide::Sell => {
                if let Some(position) = self.positions.get(symbol) {
                    position.quantity.max(0.0)
                } else {
                    0.0
                }
            }
        }
    }

    /// 获取仓位信息
    pub fn get_position(&self, symbol: &str) -> Option<&Position> {
        self.positions.get(symbol)
    }

    /// 获取所有非空仓位
    pub fn get_active_positions(&self) -> HashMap<String, &Position> {
        self.positions
            .iter()
            .filter(|(_, position)| !position.is_empty())
            .map(|(symbol, position)| (symbol.clone(), position))
            .collect()
    }

    /// 计算账户净值
    pub fn calculate_net_value(&self, current_prices: &HashMap<String, Price>) -> f64 {
        let mut net_value = self.balance_manager.get_total_balance("USDT");

        for position in self.positions.values() {
            if !position.is_empty() {
                if let Some(&current_price) = current_prices.get(&position.symbol) {
                    net_value += position.calculate_unrealized_pnl(current_price);
                }
            }
        }

        net_value
    }

    /// 验证账户状态
    pub fn validate(&self) -> Result<(), String> {
        // 验证余额
        self.balance_manager.validate_all()?;

        // 验证仓位
        for (symbol, position) in &self.positions {
            if position.symbol != *symbol {
                return Err(format!(
                    "Position symbol mismatch: {} != {}",
                    position.symbol, symbol
                ));
            }
        }

        Ok(())
    }
}

/// 账户摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountSummary {
    pub account_id: String,
    pub account_type: AccountType,
    pub stats: AccountStats,
    pub positions: Vec<PositionSummary>,
    pub balances: HashMap<String, crate::account::balance::Balance>,
    pub risk_metrics: RiskMetrics,
    pub last_updated: DateTime<Utc>,
}
