use crate::communication::message::Message;
use crate::{BacktestError, Result};
use std::collections::HashMap;
use tokio::sync::{broadcast, mpsc};
use tracing::{debug, error, info, warn};

/// 通道类型
#[derive(Debug, Clone)]
pub enum ChannelType {
    /// 广播通道（一对多）
    Broadcast(broadcast::Sender<Message>),
    /// 单向通道（一对一）
    Mpsc(mpsc::Sender<Message>),
}

/// 通道管理器
pub struct ChannelManager {
    /// 广播通道
    broadcast_channels: HashMap<String, broadcast::Sender<Message>>,
    /// 单向通道
    mpsc_channels: HashMap<String, mpsc::Sender<Message>>,
    /// 默认通道容量
    default_capacity: usize,
}

impl ChannelManager {
    /// 创建新的通道管理器
    pub fn new(default_capacity: usize) -> Self {
        Self {
            broadcast_channels: HashMap::new(),
            mpsc_channels: HashMap::new(),
            default_capacity,
        }
    }
    
    /// 创建广播通道
    pub fn create_broadcast_channel(&mut self, name: String) -> broadcast::Receiver<Message> {
        self.create_broadcast_channel_with_capacity(name, self.default_capacity)
    }
    
    /// 创建指定容量的广播通道
    pub fn create_broadcast_channel_with_capacity(
        &mut self,
        name: String,
        capacity: usize,
    ) -> broadcast::Receiver<Message> {
        let (tx, rx) = broadcast::channel(capacity);
        self.broadcast_channels.insert(name.clone(), tx);
        info!("Created broadcast channel: {} (capacity: {})", name, capacity);
        rx
    }
    
    /// 创建单向通道
    pub fn create_mpsc_channel(&mut self, name: String) -> mpsc::Receiver<Message> {
        self.create_mpsc_channel_with_capacity(name, self.default_capacity)
    }
    
    /// 创建指定容量的单向通道
    pub fn create_mpsc_channel_with_capacity(
        &mut self,
        name: String,
        capacity: usize,
    ) -> mpsc::Receiver<Message> {
        let (tx, rx) = mpsc::channel(capacity);
        self.mpsc_channels.insert(name.clone(), tx);
        info!("Created mpsc channel: {} (capacity: {})", name, capacity);
        rx
    }
    
    /// 获取广播通道发送器
    pub fn get_broadcast_sender(&self, name: &str) -> Option<broadcast::Sender<Message>> {
        self.broadcast_channels.get(name).cloned()
    }
    
    /// 获取单向通道发送器
    pub fn get_mpsc_sender(&self, name: &str) -> Option<mpsc::Sender<Message>> {
        self.mpsc_channels.get(name).cloned()
    }
    
    /// 订阅广播通道
    pub fn subscribe_broadcast(&self, name: &str) -> Option<broadcast::Receiver<Message>> {
        self.broadcast_channels.get(name).map(|tx| tx.subscribe())
    }
    
    /// 发送消息到广播通道
    pub async fn broadcast(&self, channel_name: &str, message: Message) -> Result<usize> {
        if let Some(tx) = self.broadcast_channels.get(channel_name) {
            match tx.send(message) {
                Ok(receiver_count) => {
                    debug!("Broadcasted message to {} receivers on channel: {}", receiver_count, channel_name);
                    Ok(receiver_count)
                }
                Err(e) => {
                    error!("Failed to broadcast message on channel {}: {}", channel_name, e);
                    Err(BacktestError::Communication(format!(
                        "Failed to broadcast on channel {}: {}",
                        channel_name, e
                    )))
                }
            }
        } else {
            Err(BacktestError::Communication(format!(
                "Broadcast channel not found: {}",
                channel_name
            )))
        }
    }
    
    /// 发送消息到单向通道
    pub async fn send(&self, channel_name: &str, message: Message) -> Result<()> {
        if let Some(tx) = self.mpsc_channels.get(channel_name) {
            match tx.send(message).await {
                Ok(()) => {
                    debug!("Sent message to channel: {}", channel_name);
                    Ok(())
                }
                Err(e) => {
                    error!("Failed to send message to channel {}: {}", channel_name, e);
                    Err(BacktestError::Communication(format!(
                        "Failed to send to channel {}: {}",
                        channel_name, e
                    )))
                }
            }
        } else {
            Err(BacktestError::Communication(format!(
                "Mpsc channel not found: {}",
                channel_name
            )))
        }
    }
    
    /// 检查广播通道是否存在
    pub fn has_broadcast_channel(&self, name: &str) -> bool {
        self.broadcast_channels.contains_key(name)
    }
    
    /// 检查单向通道是否存在
    pub fn has_mpsc_channel(&self, name: &str) -> bool {
        self.mpsc_channels.contains_key(name)
    }
    
    /// 移除广播通道
    pub fn remove_broadcast_channel(&mut self, name: &str) -> bool {
        if self.broadcast_channels.remove(name).is_some() {
            info!("Removed broadcast channel: {}", name);
            true
        } else {
            warn!("Attempted to remove non-existent broadcast channel: {}", name);
            false
        }
    }
    
    /// 移除单向通道
    pub fn remove_mpsc_channel(&mut self, name: &str) -> bool {
        if self.mpsc_channels.remove(name).is_some() {
            info!("Removed mpsc channel: {}", name);
            true
        } else {
            warn!("Attempted to remove non-existent mpsc channel: {}", name);
            false
        }
    }
    
    /// 获取所有广播通道名称
    pub fn broadcast_channel_names(&self) -> Vec<String> {
        self.broadcast_channels.keys().cloned().collect()
    }
    
    /// 获取所有单向通道名称
    pub fn mpsc_channel_names(&self) -> Vec<String> {
        self.mpsc_channels.keys().cloned().collect()
    }
    
    /// 获取广播通道数量
    pub fn broadcast_channel_count(&self) -> usize {
        self.broadcast_channels.len()
    }
    
    /// 获取单向通道数量
    pub fn mpsc_channel_count(&self) -> usize {
        self.mpsc_channels.len()
    }
    
    /// 清空所有通道
    pub fn clear(&mut self) {
        let broadcast_count = self.broadcast_channels.len();
        let mpsc_count = self.mpsc_channels.len();
        
        self.broadcast_channels.clear();
        self.mpsc_channels.clear();
        
        info!("Cleared {} broadcast channels and {} mpsc channels", broadcast_count, mpsc_count);
    }
    
    /// 获取通道统计信息
    pub fn get_stats(&self) -> ChannelStats {
        ChannelStats {
            broadcast_channels: self.broadcast_channels.len(),
            mpsc_channels: self.mpsc_channels.len(),
            total_channels: self.broadcast_channels.len() + self.mpsc_channels.len(),
        }
    }
}

/// 通道统计信息
#[derive(Debug, Clone)]
pub struct ChannelStats {
    pub broadcast_channels: usize,
    pub mpsc_channels: usize,
    pub total_channels: usize,
}

impl Default for ChannelManager {
    fn default() -> Self {
        Self::new(1000) // 默认容量1000
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::communication::message::{MessageType, ControlMessage};
    
    #[tokio::test]
    async fn test_broadcast_channel() {
        let mut manager = ChannelManager::new(100);
        
        // 创建广播通道
        let mut rx = manager.create_broadcast_channel("test_broadcast".to_string());
        
        // 发送消息
        let message = Message::broadcast(
            MessageType::Control(ControlMessage::Shutdown),
            "test".to_string(),
        );
        
        let result = manager.broadcast("test_broadcast", message.clone()).await;
        assert!(result.is_ok());
        
        // 接收消息
        let received = rx.recv().await.unwrap();
        assert_eq!(received.id, message.id);
    }
    
    #[tokio::test]
    async fn test_mpsc_channel() {
        let mut manager = ChannelManager::new(100);
        
        // 创建单向通道
        let mut rx = manager.create_mpsc_channel("test_mpsc".to_string());
        
        // 发送消息
        let message = Message::to(
            MessageType::Control(ControlMessage::HealthCheck {
                module: "test".to_string(),
            }),
            "sender".to_string(),
            "receiver".to_string(),
        );
        
        let result = manager.send("test_mpsc", message.clone()).await;
        assert!(result.is_ok());
        
        // 接收消息
        let received = rx.recv().await.unwrap();
        assert_eq!(received.id, message.id);
    }
    
    #[test]
    fn test_channel_management() {
        let mut manager = ChannelManager::new(100);
        
        // 创建通道
        manager.create_broadcast_channel("broadcast1".to_string());
        manager.create_mpsc_channel("mpsc1".to_string());
        
        // 检查通道存在
        assert!(manager.has_broadcast_channel("broadcast1"));
        assert!(manager.has_mpsc_channel("mpsc1"));
        
        // 获取统计信息
        let stats = manager.get_stats();
        assert_eq!(stats.broadcast_channels, 1);
        assert_eq!(stats.mpsc_channels, 1);
        assert_eq!(stats.total_channels, 2);
        
        // 移除通道
        assert!(manager.remove_broadcast_channel("broadcast1"));
        assert!(manager.remove_mpsc_channel("mpsc1"));
        
        // 验证移除
        assert!(!manager.has_broadcast_channel("broadcast1"));
        assert!(!manager.has_mpsc_channel("mpsc1"));
    }
}
