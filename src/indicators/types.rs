use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 技术指标类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum IndicatorType {
    /// 简单移动平均线
    SMA(u32), // 周期
    /// 指数移动平均线
    EMA(u32), // 周期
    /// 相对强弱指数
    RSI(u32), // 周期
    /// 布林带
    BollingerBands { period: u32, std_dev: f64 },
    /// MACD
    MACD { fast: u32, slow: u32, signal: u32 },
    /// 成交量加权平均价格
    VWAP,
    /// 平均真实波幅
    ATR(u32), // 周期
    /// 随机指标
    Stochastic { k_period: u32, d_period: u32 },
}

impl Eq for IndicatorType {}

impl std::hash::Hash for IndicatorType {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        match self {
            IndicatorType::SMA(period) => {
                "sma".hash(state);
                period.hash(state);
            }
            IndicatorType::EMA(period) => {
                "ema".hash(state);
                period.hash(state);
            }
            IndicatorType::RSI(period) => {
                "rsi".hash(state);
                period.hash(state);
            }
            IndicatorType::BollingerBands { period, std_dev } => {
                "bb".hash(state);
                period.hash(state);
                std_dev.to_bits().hash(state); // 使用bits表示来hash f64
            }
            IndicatorType::MACD { fast, slow, signal } => {
                "macd".hash(state);
                fast.hash(state);
                slow.hash(state);
                signal.hash(state);
            }
            IndicatorType::VWAP => {
                "vwap".hash(state);
            }
            IndicatorType::ATR(period) => {
                "atr".hash(state);
                period.hash(state);
            }
            IndicatorType::Stochastic { k_period, d_period } => {
                "stoch".hash(state);
                k_period.hash(state);
                d_period.hash(state);
            }
        }
    }
}

/// 指标值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IndicatorValue {
    /// 单一数值
    Single(f64),
    /// 多个数值（如布林带的上轨、中轨、下轨）
    Multiple(Vec<f64>),
    /// 键值对（如MACD的不同线）
    KeyValue(HashMap<String, f64>),
}

/// 指标配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndicatorConfig {
    pub indicator_type: IndicatorType,
    pub enabled: bool,
    pub update_interval_ms: u64,
}

/// 指标结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndicatorResult {
    pub indicator_type: IndicatorType,
    pub value: IndicatorValue,
    pub timestamp: DateTime<Utc>,
}

/// 价格数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceData {
    pub timestamp: u64, // 微秒级时间戳
    pub open: f64,
    pub high: f64,
    pub low: f64,
    pub close: f64,
    pub volume: f64,
}

impl PriceData {
    /// 创建新的价格数据点
    pub fn new(timestamp: u64, open: f64, high: f64, low: f64, close: f64, volume: f64) -> Self {
        Self {
            timestamp,
            open,
            high,
            low,
            close,
            volume,
        }
    }

    /// 获取典型价格 (HLC/3)
    pub fn typical_price(&self) -> f64 {
        (self.high + self.low + self.close) / 3.0
    }

    /// 获取加权收盘价 (HLCC/4)
    pub fn weighted_close(&self) -> f64 {
        (self.high + self.low + 2.0 * self.close) / 4.0
    }

    /// 获取真实波幅
    pub fn true_range(&self, prev_close: Option<f64>) -> f64 {
        let hl = self.high - self.low;

        if let Some(prev_close) = prev_close {
            let hc = (self.high - prev_close).abs();
            let lc = (self.low - prev_close).abs();
            hl.max(hc).max(lc)
        } else {
            hl
        }
    }
}

impl IndicatorValue {
    /// 获取单一数值
    pub fn as_single(&self) -> Option<f64> {
        match self {
            IndicatorValue::Single(value) => Some(*value),
            _ => None,
        }
    }

    /// 获取多个数值
    pub fn as_multiple(&self) -> Option<&Vec<f64>> {
        match self {
            IndicatorValue::Multiple(values) => Some(values),
            _ => None,
        }
    }

    /// 获取键值对
    pub fn as_key_value(&self) -> Option<&HashMap<String, f64>> {
        match self {
            IndicatorValue::KeyValue(map) => Some(map),
            _ => None,
        }
    }

    /// 获取指定键的值
    pub fn get_value(&self, key: &str) -> Option<f64> {
        match self {
            IndicatorValue::Single(value) if key == "value" => Some(*value),
            IndicatorValue::KeyValue(map) => map.get(key).copied(),
            _ => None,
        }
    }
}

impl Default for IndicatorConfig {
    fn default() -> Self {
        Self {
            indicator_type: IndicatorType::SMA(20),
            enabled: true,
            update_interval_ms: 1000,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_price_data() {
        let timestamp = chrono::Utc::now().timestamp_micros() as u64;
        let price = PriceData::new(timestamp, 100.0, 105.0, 95.0, 102.0, 1000.0);

        assert_eq!(price.typical_price(), (105.0 + 95.0 + 102.0) / 3.0);
        assert_eq!(price.weighted_close(), (105.0 + 95.0 + 2.0 * 102.0) / 4.0);
        assert_eq!(price.true_range(Some(98.0)), 105.0 - 95.0);
    }

    #[test]
    fn test_indicator_value() {
        let single = IndicatorValue::Single(42.0);
        assert_eq!(single.as_single(), Some(42.0));
        assert_eq!(single.get_value("value"), Some(42.0));

        let mut map = HashMap::new();
        map.insert("macd".to_string(), 1.5);
        map.insert("signal".to_string(), 1.2);
        let key_value = IndicatorValue::KeyValue(map);
        assert_eq!(key_value.get_value("macd"), Some(1.5));
        assert_eq!(key_value.get_value("signal"), Some(1.2));
    }
}
