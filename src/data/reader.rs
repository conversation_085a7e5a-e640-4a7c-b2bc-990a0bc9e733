use crate::types::MarketData;
use crate::{BacktestError, Result};
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};

use tracing::{error, info};

/// 数据读取器状态
#[derive(Debug, Clone, PartialEq)]
pub enum DataReaderStatus {
    /// 空闲状态
    Idle,
    /// 读取中
    Reading,
    /// 暂停状态
    Paused,
    /// 停止状态
    Stopped,
    /// 错误状态
    Error(String),
}

/// 数据读取器控制命令
#[derive(Debug, Clone)]
pub enum ReaderControlCommand {
    /// 开始读取
    Start,
    /// 暂停读取
    Pause,
    /// 恢复读取
    Resume,
    /// 停止读取
    Stop,
}

/// 数据读取器
/// 专注于流程控制，实际数据处理由 DataStreamer 负责
pub struct DataReader {
    status: Arc<Mutex<DataReaderStatus>>,
    output_tx: Option<mpsc::Sender<MarketData>>,
}

impl DataReader {
    /// 创建新的数据读取器
    pub fn new() -> Result<Self> {
        Ok(Self {
            status: Arc::new(Mutex::new(DataReaderStatus::Idle)),
            output_tx: None,
        })
    }

    /// 获取当前状态
    pub async fn get_status(&self) -> DataReaderStatus {
        self.status.lock().await.clone()
    }

    /// 启动读取器
    pub async fn start(&mut self) -> Result<()> {
        // 检查是否已经准备好输出通道
        let output_tx = self
            .output_tx
            .as_ref()
            .ok_or_else(|| {
                BacktestError::Data(
                    "Output channel not prepared. Call prepare_reading first.".to_string(),
                )
            })?
            .clone();

        // 设置状态为 Reading
        *self.status.lock().await = DataReaderStatus::Reading;

        // 启动数据流任务
        let config = crate::config::ConfigManager::get()?;
        let status = self.status.clone();

        info!("Starting {} data source(s)", config.data_paths.len());
        for data_path in config.data_paths.iter() {
            let output_tx_clone = output_tx.clone();
            let status_clone = status.clone();
            let start_time = config.start_time;
            let end_time = config.end_time;

            match data_path.data_source_type {
                crate::config::DataSourceType::BinanceOfficial => {
                    tokio::spawn(async move {
                        info!("BinanceOfficial data source not yet implemented for streaming");
                    });
                }
                crate::config::DataSourceType::BinanceTardis => {
                    info!("Starting Binance Tardis data streaming");
                    let data_paths_clone = data_path.sources.clone();
                    let handle = tokio::spawn(async move {
                        tracing::debug!("🔥 Binance Tardis task started - creating streamer");
                        let streamer = crate::data::streamer::DataStreamer::new(status_clone);
                        tracing::debug!(
                            "🔥 Binance Tardis streamer created - calling start_streaming"
                        );
                        match streamer
                            .start_streaming(
                                data_paths_clone,
                                output_tx_clone,
                                start_time,
                                end_time,
                                crate::config::DataSourceType::BinanceTardis,
                            )
                            .await
                        {
                            Ok(_) => info!("🔥 Binance Tardis streaming completed successfully"),
                            Err(e) => error!("🔥 Failed to stream Binance Tardis data: {}", e),
                        }
                    });

                    // 存储任务句柄以便后续检查
                    tokio::spawn(async move {
                        if let Err(e) = handle.await {
                            error!("🔥 Binance Tardis task panicked: {}", e);
                        }
                    });
                }
                crate::config::DataSourceType::OkxTardis => {
                    info!("Starting OKX Tardis data streaming");
                    let data_paths_clone = data_path.sources.clone();
                    tokio::spawn(async move {
                        let streamer = crate::data::streamer::DataStreamer::new(status_clone);
                        if let Err(e) = streamer
                            .start_streaming(
                                data_paths_clone,
                                output_tx_clone,
                                start_time,
                                end_time,
                                crate::config::DataSourceType::OkxTardis,
                            )
                            .await
                        {
                            error!("Failed to stream OKX Tardis data: {}", e);
                        }
                    });
                }
            }
        }

        info!("Data reading started - streaming tasks launched");
        Ok(())
    }

    /// 暂停读取器
    pub async fn pause(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Paused;
        info!("Data reading paused");
        Ok(())
    }

    /// 恢复读取器
    pub async fn resume(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Reading;
        info!("Data reading resumed");
        Ok(())
    }

    /// 停止读取器
    pub async fn stop(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Stopped;
        info!("Data reading stopped");
        Ok(())
    }

    /// 准备数据读取（只负责流程控制，不启动数据流）
    /// 数据流将在 start() 被调用后启动
    pub async fn prepare_reading(&mut self, output_tx: mpsc::Sender<MarketData>) -> Result<()> {
        info!("Preparing data reading - storing output channel");

        // 只存储输出通道，不启动数据流
        self.output_tx = Some(output_tx);

        info!("Data reading prepared - waiting for start command");
        Ok(())
    }
}
