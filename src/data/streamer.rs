use crate::config::{DataSourceType, DataTypesPaths};
use crate::data::parser::DataParser;
use crate::data::reader::DataReaderStatus;
use crate::types::MarketData;
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use flate2::read::GzDecoder;
use std::io::BufRead;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::sync::{mpsc, Mutex};
use tracing::{error, info, warn};

/// 数据流处理器
/// 负责实际的数据解析和发送，与 DataReader 分离
pub struct DataStreamer {
    status: Arc<Mutex<DataReaderStatus>>,
}

impl DataStreamer {
    pub fn new(status: Arc<Mutex<DataReaderStatus>>) -> Self {
        Self { status }
    }

    /// 启动数据流处理任务（改为先收集并排序后再发送）
    pub async fn start_streaming(
        &self,
        data_paths: DataTypesPaths,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
    ) -> Result<()> {
        info!(
            "Starting data streaming (collect-sort-send) for {:?}",
            data_source_type
        );

        // 1) 等待状态变为 Reading
        loop {
            let current_status = self.status.lock().await.clone();
            match current_status {
                DataReaderStatus::Reading => break,
                DataReaderStatus::Stopped => {
                    info!("Streaming stopped before start");
                    return Ok(());
                }
                _ => {
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    continue;
                }
            }
        }

        // 2) 收集各数据源的数据（仅保留 [start_time, end_time]）
        let mut all_data: Vec<MarketData> = Vec::new();

        if let Some(quotes_path) = data_paths.quotes.clone() {
            let mut v = Self::collect_quotes_data(
                quotes_path,
                start_time,
                end_time,
                data_source_type,
                self.status.clone(),
            )
            .await?;
            all_data.append(&mut v);
        }

        if let Some(trades_path) = data_paths.trades.clone() {
            let mut v = Self::collect_trades_data(
                trades_path,
                start_time,
                end_time,
                data_source_type,
                self.status.clone(),
            )
            .await?;
            all_data.append(&mut v);
        }

        if let Some(depth_path) = data_paths.depth.clone() {
            let mut v = Self::collect_depth_data(
                depth_path,
                start_time,
                end_time,
                data_source_type,
                self.status.clone(),
            )
            .await?;
            all_data.append(&mut v);
        }

        info!(
            "Collected {} market data records; sorting by timestamp",
            all_data.len()
        );

        // 3) 排序（按时间戳）
        all_data.sort_by_key(|md| md.timestamp_for_sorting());

        // 4) 按顺序发送到引擎
        for md in all_data.into_iter() {
            // 在发送前检查状态
            let current_status = self.status.lock().await.clone();
            if !matches!(current_status, DataReaderStatus::Reading) {
                info!("Streaming stopped during send phase");
                return Ok(());
            }

            if let Err(e) = output_tx.send(md).await {
                error!("Failed to send market data: {}", e);
                return Err(BacktestError::Data(format!("Failed to send data: {}", e)));
            }

            // 避免过快发送，给其他任务让出调度
            tokio::task::yield_now().await;
        }

        info!("Collect-sort-send streaming completed");
        Ok(())
    }

    /// 根据文件名找到包含 start_time 的文件索引
    fn find_start_file(files: &[PathBuf], start_time: DateTime<Utc>) -> Result<Option<usize>> {
        let start_date = start_time.format("%Y-%m-%d").to_string();

        for (index, file_path) in files.iter().enumerate() {
            if let Some(file_name) = file_path.file_name().and_then(|n| n.to_str()) {
                // 检查文件名是否以日期开头（标准格式）
                if file_name.starts_with(&start_date) {
                    tracing::debug!("Found start file: {} at index {}", file_name, index);
                    return Ok(Some(index));
                }

                // 检查文件名是否包含日期（如 Binance depth 格式）
                if file_name.contains(&start_date) {
                    tracing::debug!(
                        "Found start file (contains date): {} at index {}",
                        file_name,
                        index
                    );
                    return Ok(Some(index));
                }
            }
        }

        warn!("No file found for start date: {}", start_date);
        Ok(None)
    }

    /// 收集 quotes 数据到内存（仅 [start_time, end_time]）
    async fn collect_quotes_data(
        quotes_path: PathBuf,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: Arc<Mutex<DataReaderStatus>>,
    ) -> Result<Vec<MarketData>> {
        let mut result = Vec::new();
        let files = Self::find_files(&quotes_path).await?;
        let start_file_index = Self::find_start_file(&files, start_time)?;

        if let Some(start_index) = start_file_index {
            for file_path in &files[start_index..] {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        info!("Quotes collection stopped");
                        return Ok(result);
                    }
                }

                tracing::info!("Collecting quotes from file: {:?}", file_path);

                // 压缩与非压缩分别处理
                if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                    let file = std::fs::File::open(file_path)
                        .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
                    let decoder = GzDecoder::new(file);
                    let reader = std::io::BufReader::new(decoder);

                    for line in reader.lines() {
                        // 状态检查
                        {
                            let current_status = status.lock().await.clone();
                            if !matches!(current_status, DataReaderStatus::Reading) {
                                return Ok(result);
                            }
                        }

                        let line = line.map_err(|e| {
                            BacktestError::Data(format!("Failed to read line: {}", e))
                        })?;
                        if line.trim().is_empty() {
                            continue;
                        }

                        match DataParser::parse_quotes_csv_line_with_path(
                            &line,
                            data_source_type,
                            Some(file_path.to_string_lossy().as_ref()),
                        ) {
                            Ok(Some(market_data)) => {
                                if DataParser::is_data_in_time_range(
                                    &market_data,
                                    start_time,
                                    end_time,
                                ) {
                                    result.push(market_data);
                                }
                            }
                            Ok(None) => {}
                            Err(e) => {
                                error!("Failed to parse quotes line: {}, line: {}", e, line);
                            }
                        }

                        tokio::task::yield_now().await;
                    }
                } else {
                    let file = tokio::fs::File::open(file_path)
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
                    let reader = BufReader::new(file);
                    let mut lines = reader.lines();

                    while let Some(line) = lines
                        .next_line()
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
                    {
                        // 状态检查
                        {
                            let current_status = status.lock().await.clone();
                            if !matches!(current_status, DataReaderStatus::Reading) {
                                return Ok(result);
                            }
                        }

                        if line.trim().is_empty() {
                            continue;
                        }

                        match DataParser::parse_quotes_csv_line_with_path(
                            &line,
                            data_source_type,
                            Some(file_path.to_string_lossy().as_ref()),
                        ) {
                            Ok(Some(market_data)) => {
                                if DataParser::is_data_in_time_range(
                                    &market_data,
                                    start_time,
                                    end_time,
                                ) {
                                    result.push(market_data);
                                }
                            }
                            Ok(None) => {}
                            Err(e) => {
                                error!("Failed to parse quotes line: {}, line: {}", e, line);
                            }
                        }

                        tokio::task::yield_now().await;
                    }
                }

                if Self::file_exceeds_end_time(file_path, end_time) {
                    info!("Reached end time during quotes collection");
                    break;
                }
            }
        }

        Ok(result)
    }

    /// 收集 trades 数据到内存（仅 [start_time, end_time]）
    async fn collect_trades_data(
        trades_path: PathBuf,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: Arc<Mutex<DataReaderStatus>>,
    ) -> Result<Vec<MarketData>> {
        let mut result = Vec::new();
        let files = Self::find_files(&trades_path).await?;
        let start_file_index = Self::find_start_file(&files, start_time)?;

        if let Some(start_index) = start_file_index {
            for file_path in &files[start_index..] {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        info!("Trades collection stopped");
                        return Ok(result);
                    }
                }

                tracing::info!("Collecting trades from file: {:?}", file_path);

                if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                    let file = std::fs::File::open(file_path)
                        .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
                    let decoder = GzDecoder::new(file);
                    let reader = std::io::BufReader::new(decoder);

                    for line in reader.lines() {
                        // 状态检查
                        {
                            let current_status = status.lock().await.clone();
                            if !matches!(current_status, DataReaderStatus::Reading) {
                                return Ok(result);
                            }
                        }

                        let line = line.map_err(|e| {
                            BacktestError::Data(format!("Failed to read line: {}", e))
                        })?;
                        if line.trim().is_empty() {
                            continue;
                        }

                        match DataParser::parse_trades_csv_line_with_path(
                            &line,
                            data_source_type,
                            Some(file_path.to_string_lossy().as_ref()),
                        ) {
                            Ok(Some(market_data)) => {
                                if DataParser::is_data_in_time_range(
                                    &market_data,
                                    start_time,
                                    end_time,
                                ) {
                                    result.push(market_data);
                                }
                            }
                            Ok(None) => {}
                            Err(e) => {
                                error!("Failed to parse trades line: {}, line: {}", e, line);
                            }
                        }

                        tokio::task::yield_now().await;
                    }
                } else {
                    let file = tokio::fs::File::open(file_path)
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
                    let reader = BufReader::new(file);
                    let mut lines = reader.lines();

                    while let Some(line) = lines
                        .next_line()
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
                    {
                        // 状态检查
                        {
                            let current_status = status.lock().await.clone();
                            if !matches!(current_status, DataReaderStatus::Reading) {
                                return Ok(result);
                            }
                        }

                        if line.trim().is_empty() {
                            continue;
                        }

                        match DataParser::parse_trades_csv_line_with_path(
                            &line,
                            data_source_type,
                            Some(file_path.to_string_lossy().as_ref()),
                        ) {
                            Ok(Some(market_data)) => {
                                if DataParser::is_data_in_time_range(
                                    &market_data,
                                    start_time,
                                    end_time,
                                ) {
                                    result.push(market_data);
                                }
                            }
                            Ok(None) => {}
                            Err(e) => {
                                error!("Failed to parse trades line: {}, line: {}", e, line);
                            }
                        }

                        tokio::task::yield_now().await;
                    }
                }

                if Self::file_exceeds_end_time(file_path, end_time) {
                    info!("Reached end time during trades collection");
                    break;
                }
            }
        }

        Ok(result)
    }

    /// 收集 depth 数据到内存（仅 [start_time, end_time]）
    async fn collect_depth_data(
        depth_path: PathBuf,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        data_source_type: DataSourceType,
        status: Arc<Mutex<DataReaderStatus>>,
    ) -> Result<Vec<MarketData>> {
        let mut result = Vec::new();
        let files = Self::find_files(&depth_path).await?;
        let start_file_index = Self::find_start_file(&files, start_time)?;

        if let Some(start_index) = start_file_index {
            for file_path in &files[start_index..] {
                // 检查状态
                {
                    let current_status = status.lock().await.clone();
                    if !matches!(current_status, DataReaderStatus::Reading) {
                        info!("Depth collection stopped");
                        return Ok(result);
                    }
                }

                tracing::debug!("Collecting depth from file: {:?}", file_path);

                if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                    let file = std::fs::File::open(file_path)
                        .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
                    let decoder = GzDecoder::new(file);
                    let reader = std::io::BufReader::new(decoder);

                    for line in reader.lines() {
                        // 状态检查
                        {
                            let current_status = status.lock().await.clone();
                            if !matches!(current_status, DataReaderStatus::Reading) {
                                return Ok(result);
                            }
                        }

                        let line = line.map_err(|e| {
                            BacktestError::Data(format!("Failed to read line: {}", e))
                        })?;
                        if line.trim().is_empty() {
                            continue;
                        }

                        match DataParser::parse_depth_csv_line_with_path(
                            &line,
                            data_source_type,
                            Some(file_path.to_string_lossy().as_ref()),
                        )
                        .await
                        {
                            Ok(Some(market_data)) => {
                                if DataParser::is_data_in_time_range(
                                    &market_data,
                                    start_time,
                                    end_time,
                                ) {
                                    result.push(market_data);
                                }
                            }
                            Ok(None) => {}
                            Err(e) => {
                                error!("Failed to parse depth line: {}, line: {}", e, line);
                            }
                        }

                        tokio::task::yield_now().await;
                    }
                } else {
                    let file = tokio::fs::File::open(file_path)
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to open file: {}", e)))?;
                    let reader = BufReader::new(file);
                    let mut lines = reader.lines();

                    while let Some(line) = lines
                        .next_line()
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
                    {
                        // 状态检查
                        {
                            let current_status = status.lock().await.clone();
                            if !matches!(current_status, DataReaderStatus::Reading) {
                                return Ok(result);
                            }
                        }

                        if line.trim().is_empty() {
                            continue;
                        }

                        match DataParser::parse_depth_csv_line_with_path(
                            &line,
                            data_source_type,
                            Some(file_path.to_string_lossy().as_ref()),
                        )
                        .await
                        {
                            Ok(Some(market_data)) => {
                                if DataParser::is_data_in_time_range(
                                    &market_data,
                                    start_time,
                                    end_time,
                                ) {
                                    result.push(market_data);
                                }
                            }
                            Ok(None) => {}
                            Err(e) => {
                                error!("Failed to parse depth line: {}, line: {}", e, line);
                            }
                        }

                        tokio::task::yield_now().await;
                    }
                }

                if Self::file_exceeds_end_time(file_path, end_time) {
                    info!("Reached end time during depth collection");
                    break;
                }
            }
        }

        Ok(result)
    }

    /// 检查文件是否超过结束时间
    fn file_exceeds_end_time(file_path: &PathBuf, end_time: DateTime<Utc>) -> bool {
        if let Some(file_name) = file_path.file_name().and_then(|n| n.to_str()) {
            if let Some(date_part) = file_name.split('_').next() {
                if let Ok(file_date) = chrono::NaiveDate::parse_from_str(date_part, "%Y-%m-%d") {
                    let file_datetime = file_date.and_hms_opt(23, 59, 59).unwrap().and_utc();
                    return file_datetime > end_time;
                }
            }
        }
        false
    }

    /// 查找数据文件
    async fn find_files(path: &PathBuf) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let mut entries = tokio::fs::read_dir(path)
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if extension == "csv" || extension == "gz" {
                        files.push(path);
                    }
                }
            }
        }

        files.sort();
        Ok(files)
    }
}
