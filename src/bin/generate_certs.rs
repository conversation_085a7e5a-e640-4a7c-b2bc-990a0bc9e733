use rcgen::{Certificate, CertificateParams, DistinguishedName, DnType};
use std::fs;
use std::path::Path;
use time::{Duration, OffsetDateTime};
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    let cert_dir = Path::new("./certs");
    let cert_path = cert_dir.join("server.crt");
    let key_path = cert_dir.join("server.key");

    // 创建证书目录
    if !cert_dir.exists() {
        fs::create_dir_all(cert_dir)?;
        info!("Created certificate directory: {:?}", cert_dir);
    }

    // 检查证书是否已存在
    if cert_path.exists() && key_path.exists() {
        warn!("Certificates already exist:");
        warn!("  Certificate: {:?}", cert_path);
        warn!("  Private Key: {:?}", key_path);
        warn!("Remove them first if you want to regenerate.");
        return Ok(());
    }

    info!("Generating self-signed certificates for development...");

    // 创建证书参数
    let mut params = CertificateParams::new(vec!["localhost".to_string()]);

    // 设置证书参数
    params.not_before = OffsetDateTime::now_utc() - Duration::days(1);
    params.not_after = OffsetDateTime::now_utc() + Duration::days(365);

    // 设置主题名称
    let mut distinguished_name = DistinguishedName::new();
    distinguished_name.push(DnType::CommonName, "localhost");
    params.distinguished_name = distinguished_name;

    // 生成证书
    let cert = Certificate::from_params(params)?;

    // 将证书写入文件
    let cert_pem = cert.serialize_pem()?;
    fs::write(&cert_path, cert_pem)?;
    info!("Certificate written to: {:?}", cert_path);

    // 将私钥写入文件
    let key_pem = cert.serialize_private_key_pem();
    fs::write(&key_path, key_pem)?;
    info!("Private key written to: {:?}", key_path);

    // 设置适当的权限（仅在Unix系统上）
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;

        // 私钥文件权限设为600 (仅所有者可读写)
        let mut perms = fs::metadata(&key_path)?.permissions();
        perms.set_mode(0o600);
        fs::set_permissions(&key_path, perms)?;

        // 证书文件权限设为644 (所有者可读写，其他人只读)
        let mut perms = fs::metadata(&cert_path)?.permissions();
        perms.set_mode(0o644);
        fs::set_permissions(&cert_path, perms)?;

        info!("File permissions set appropriately");
    }

    info!("Certificates generated successfully!");
    info!("");
    info!("To use these certificates, update your config.toml:");
    info!("");
    info!("[http_tls]");
    info!("enabled = true");
    info!("");
    info!("[http_tls.cert_source]");
    info!("type = \"Files\"");
    info!("cert_path = \"{}\"", cert_path.display());
    info!("key_path = \"{}\"", key_path.display());
    info!("");
    info!("[websocket_tls]");
    info!("enabled = true");
    info!("");
    info!("[websocket_tls.cert_source]");
    info!("type = \"Files\"");
    info!("cert_path = \"{}\"", cert_path.display());
    info!("key_path = \"{}\"", key_path.display());
    info!("");
    warn!("WARNING: These are self-signed certificates for development only!");
    warn!("Your browser will show security warnings when accessing HTTPS endpoints.");

    Ok(())
}
