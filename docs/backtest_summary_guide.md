# 回测总结功能使用指南

## 概述

回测总结功能是回测框架的重要组成部分，它能够自动记录回测过程中的所有关键数据，并在回测结束后生成详细的HTML报告，帮助用户分析交易策略的表现。

## 功能特性

### 1. 数据记录
- **BBO数据记录**: 自动记录所有最优买卖价数据
- **订单记录**: 记录所有下单、成交、取消、过期等订单状态
- **交易记录**: 记录所有成交的交易，包括价格、数量、手续费、盈亏等

### 2. 统计分析
- **总盈亏**: 计算回测期间的总收益
- **下单次数**: 统计总订单数量
- **成交次数**: 统计成功成交的交易数量
- **胜率**: 计算盈利交易占总交易的比例
- **年化收益率**: 将回测收益年化计算
- **最大回撤**: 计算回测期间的最大亏损幅度

### 3. 可视化报告
- **K线图**: 显示价格走势和交易点位
- **统计卡片**: 直观展示关键指标
- **交易记录表**: 详细的交易历史
- **订单记录表**: 完整的订单信息

## 使用方法

### 1. 启动回测记录

通过HTTP API开始记录：

```bash
curl -X POST http://localhost:8080/api/v1/backtest/start
```

响应示例：
```json
{
  "success": true,
  "message": "Backtest started at 2024-01-01 10:00:00",
  "error": null
}
```

### 2. 停止回测记录

通过HTTP API停止记录：

```bash
curl -X POST http://localhost:8080/api/v1/backtest/stop
```

响应示例：
```json
{
  "success": true,
  "message": "Backtest stopped at 2024-01-01 11:00:00",
  "error": null
}
```

### 3. 获取回测总结

#### 方式一：JSON格式
通过HTTP API获取JSON格式的报告：

```bash
curl -X GET http://localhost:8080/api/v1/backtest/summary
```

响应示例：
```json
{
  "success": true,
  "data": "<!DOCTYPE html>...",
  "error": null
}
```

#### 方式二：直接访问HTML页面
在浏览器中直接访问HTML页面：

```bash
# 在浏览器中打开
http://localhost:8080/backtest/summary
```

或者使用curl查看HTML内容：
```bash
curl http://localhost:8080/backtest/summary
```

这种方式更适合在Linux服务器上使用，因为可以直接在浏览器中查看完整的回测报告。

### 4. 程序化使用

```rust
use backtest::types::BacktestRecorder;
use backtest::state::{set_backtest_recorder, get_backtest_recorder};
use backtest::backtest_summary::HtmlGenerator;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::Utc;

#[tokio::main]
async fn main() {
    // 1. 初始化记录器
    let recorder = Arc::new(Mutex::new(BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;

    // 2. 开始记录
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }

    // 3. 执行回测逻辑...
    // 数据会自动记录到记录器中

    // 4. 停止记录
    {
        let mut recorder = recorder.lock().await;
        recorder.stop_recording(Utc::now());
    }

    // 5. 生成总结
    let summary = {
        let recorder = recorder.lock().await;
        recorder.generate_summary()
    };

    if let Some(summary) = summary {
        // 6. 生成HTML报告
        let html = HtmlGenerator::generate_summary_html(&summary);
        std::fs::write("backtest_report.html", html).unwrap();
    }
}
```

## 数据结构

### BacktestSummary
```rust
pub struct BacktestSummary {
    pub start_time: DateTime<Utc>,           // 回测开始时间
    pub end_time: DateTime<Utc>,             // 回测结束时间
    pub total_pnl: f64,                      // 总盈亏
    pub total_orders: usize,                 // 下单次数
    pub total_fills: usize,                  // 成交次数
    pub win_rate: f64,                       // 胜率
    pub annual_return: f64,                  // 年化收益率
    pub max_drawdown: f64,                   // 最大回撤
    pub trades: Vec<BacktestTrade>,          // 交易记录
    pub orders: Vec<BacktestOrder>,          // 订单记录
    pub bbo_records: Vec<BacktestBbo>,       // BBO记录
}
```

### BacktestTrade
```rust
pub struct BacktestTrade {
    pub trade_id: String,                    // 交易ID
    pub order_id: String,                    // 订单ID
    pub symbol: String,                      // 交易对
    pub side: OrderSide,                     // 交易方向
    pub price: Price,                        // 成交价格
    pub quantity: f64,                       // 成交数量
    pub fee: f64,                           // 手续费
    pub fee_asset: String,                   // 手续费币种
    pub timestamp: DateTime<Utc>,            // 交易时间
    pub is_maker: bool,                      // 是否为maker
    pub pnl: f64,                           // 盈亏
}
```

### BacktestOrder
```rust
pub struct BacktestOrder {
    pub order_id: String,                    // 订单ID
    pub client_order_id: String,             // 客户端订单ID
    pub symbol: String,                      // 交易对
    pub order_type: OrderType,               // 订单类型
    pub side: OrderSide,                     // 订单方向
    pub price: Option<Price>,                // 订单价格
    pub quantity: f64,                       // 订单数量
    pub status: OrderStatus,                 // 订单状态
    pub timestamp: DateTime<Utc>,            // 下单时间
    pub filled_price: Option<Price>,         // 成交价格
    pub filled_quantity: f64,                // 成交数量
    pub fee: f64,                           // 手续费
    pub fee_asset: String,                   // 手续费币种
}
```

## 自动记录机制

### 1. BBO数据自动记录
在数据处理器中，所有BBO数据会自动记录到回测记录器：

```rust
// 在 DataProcessor::process_market_data 中
async fn record_market_data(&self, market_data: &MarketData) {
    if let MarketData::Bbo(bbo) = market_data {
        if let Some(recorder) = get_backtest_recorder().await {
            let mut recorder = recorder.lock().await;
            recorder.record_bbo(bbo);
        }
    }
}
```

### 2. 交易数据自动记录
在账户管理器中，所有交易会自动记录：

```rust
// 在 AccountManager::process_trade 中
fn record_trade(&self, trade_record: &TradeRecord) {
    // 异步记录交易到回测记录器
    if let Some(recorder) = get_backtest_recorder().await {
        let mut recorder = recorder.lock().await;
        recorder.record_trade(&trade_record);
    }
}
```

## 报告内容

### 1. 统计摘要
- 总盈亏（USDT）
- 年化收益率（%）
- 胜率（%）
- 最大回撤（%）
- 下单次数
- 成交次数

### 2. K线图与交易点位
- 价格走势线图
- 买入点位标记（绿色）
- 卖出点位标记（红色）
- 时间轴和价格轴

### 3. 交易记录表
- 交易时间
- 交易方向
- 成交价格
- 成交数量
- 手续费
- 盈亏

### 4. 订单记录表
- 下单时间
- 订单方向
- 订单价格
- 订单数量
- 成交数量
- 成交价格
- 手续费

## 注意事项

1. **内存使用**: 大量数据记录可能占用较多内存，建议定期清理或限制记录数量
2. **性能影响**: 记录功能会轻微影响回测性能，但影响很小
3. **数据完整性**: 确保在回测开始前调用start_recording，结束后调用stop_recording
4. **并发安全**: 记录器使用Mutex保证线程安全，支持并发访问

## 故障排除

### 1. 记录器未初始化
错误信息：`Backtest recorder not initialized`
解决方案：确保在框架启动时正确初始化了回测记录器

### 2. 没有回测数据
错误信息：`No backtest data available`
解决方案：确保在获取总结前已经记录了数据

### 3. HTML生成失败
错误信息：`Failed to generate backtest summary`
解决方案：检查数据格式是否正确，确保所有必要字段都有值

## 扩展功能

### 1. 自定义指标
可以在BacktestSummary中添加更多自定义指标：

```rust
pub struct BacktestSummary {
    // ... 现有字段 ...
    pub sharpe_ratio: f64,                   // 夏普比率
    pub sortino_ratio: f64,                  // 索提诺比率
    pub calmar_ratio: f64,                   // 卡玛比率
    pub max_consecutive_losses: usize,       // 最大连续亏损次数
}
```

### 2. 自定义图表
可以在HTML生成器中添加更多图表类型：

```rust
// 添加成交量图表
fn generate_volume_chart(&self, trades: &[BacktestTrade]) -> String {
    // 实现成交量图表生成逻辑
}
```

### 3. 数据导出
可以添加数据导出功能：

```rust
pub fn export_to_csv(&self, summary: &BacktestSummary) -> Result<()> {
    // 实现CSV导出逻辑
}
```

## 总结

回测总结功能提供了完整的回测数据记录和分析能力，通过自动记录和可视化报告，帮助用户更好地理解和优化交易策略。该功能设计简洁、使用方便、扩展性强，是回测框架的重要补充。
