# 订单延迟模拟功能

## 概述

订单延迟模拟功能允许在回测系统中模拟真实交易环境中的订单执行延迟。这个功能基于市场数据的时间戳而不是系统时间，确保了回测的准确性和可重复性。

## 功能特性

- **基于市场数据时间戳的延迟** - 使用市场数据的时间戳计算延迟，而不是系统时间
- **可配置的延迟时间** - 支持微秒级精度的延迟配置
- **随机延迟模拟** - 可选的随机延迟功能，模拟真实网络环境的不确定性
- **订单队列管理** - 自动管理延迟订单队列，按时间戳排序
- **实时配置更新** - 支持运行时动态更新配置
- **统计信息** - 提供详细的延迟队列统计信息

## 配置参数

### OrderLatencyConfig

```toml
[playback.order_latency]
# 是否启用订单延迟模拟
enabled = false
# 订单延迟时间（微秒）- 默认3ms
latency_micros = 3000
# 延迟队列最大大小
max_queue_size = 10000
# 是否启用随机延迟（在延迟时间的80%-120%范围内随机）
random_latency = false
```

### 配置说明

- **enabled**: 控制是否启用延迟模拟。禁用时订单立即执行
- **latency_micros**: 基础延迟时间，单位为微秒（1ms = 1000微秒）
- **max_queue_size**: 延迟队列的最大容量，防止内存无限增长
- **random_latency**: 启用后，实际延迟在 `[latency_micros * 0.8, latency_micros * 1.2]` 范围内随机

## 使用示例

### 1. 基本配置

```toml
# example_config.toml
[playback.order_latency]
enabled = true
latency_micros = 5000  # 5ms延迟
max_queue_size = 10000
random_latency = false
```

### 2. 随机延迟配置

```toml
[playback.order_latency]
enabled = true
latency_micros = 3000   # 基础3ms延迟
max_queue_size = 10000
random_latency = true   # 实际延迟在2.4ms-3.6ms之间
```

### 3. 高频交易模拟

```toml
[playback.order_latency]
enabled = true
latency_micros = 100    # 100微秒延迟
max_queue_size = 50000
random_latency = true
```

## 工作原理

### 延迟计算

1. **订单提交时间**: 使用当前市场数据的时间戳作为订单提交时间
2. **执行时间计算**: 执行时间 = 提交时间 + 延迟时间
3. **随机延迟**: 如果启用，延迟时间在配置值的80%-120%范围内随机

### 订单处理流程

```
订单提交 → 检查延迟配置 → 添加到延迟队列 → 等待执行时间 → 执行订单
    ↓              ↓                ↓              ↓           ↓
市场时间戳    计算执行时间    按时间排序      时间到达      撮合执行
```

### 时间同步

- 每当处理新的市场数据时，系统会更新当前市场时间戳
- 检查延迟队列中是否有订单可以执行
- 按时间顺序执行所有到期的订单

## API 接口

### 获取延迟统计信息

```rust
let stats = matching_engine.get_order_latency_stats();
println!("队列大小: {}", stats.queue_size);
println!("延迟时间: {}微秒", stats.latency_micros);
println!("随机延迟: {}", stats.random_latency);
```

### 清空延迟队列

```rust
matching_engine.clear_delayed_orders();
```

### 获取当前市场时间戳

```rust
let timestamp = matching_engine.get_current_market_timestamp();
```

## 性能考虑

### 内存使用

- 延迟队列使用 `VecDeque` 存储订单，内存使用与队列大小成正比
- 建议根据实际需求设置合适的 `max_queue_size`
- 系统会自动清理超出限制的旧订单

### 计算复杂度

- 添加订单: O(n) - 需要找到正确的插入位置保持时间排序
- 获取可执行订单: O(k) - k为可执行订单数量
- 随机数生成: O(1) - 使用高效的随机数生成器

### 优化建议

1. **合理设置队列大小**: 避免队列过大导致内存问题
2. **批量处理**: 在高频场景下考虑批量处理订单
3. **监控统计**: 定期检查队列统计信息，调整配置

## 测试验证

运行延迟模拟演示：

```bash
cargo run --example order_latency_demo
```

运行单元测试：

```bash
cargo test order_latency_simulator
```

## 实际应用场景

### 1. 网络延迟模拟

模拟订单从客户端到交易所的网络传输延迟：

```toml
latency_micros = 2000    # 2ms网络延迟
random_latency = true    # 模拟网络抖动
```

### 2. 交易所处理延迟

模拟交易所内部订单处理延迟：

```toml
latency_micros = 500     # 500微秒处理延迟
random_latency = false   # 稳定的处理时间
```

### 3. 高频交易测试

测试高频交易策略在延迟环境下的表现：

```toml
latency_micros = 50      # 50微秒超低延迟
random_latency = true    # 模拟微小的时间抖动
```

## 注意事项

1. **时间精度**: 延迟时间基于市场数据时间戳，精度取决于数据源
2. **队列管理**: 系统会自动管理队列大小，但建议监控队列状态
3. **配置更新**: 配置更新会立即生效，但不影响已在队列中的订单
4. **禁用状态**: 禁用延迟模拟时，订单会立即执行，不进入延迟队列

## 故障排除

### 常见问题

1. **订单未执行**: 检查延迟时间设置和当前市场时间戳
2. **队列过大**: 调整 `max_queue_size` 或检查订单处理逻辑
3. **随机延迟异常**: 验证随机数生成器是否正常工作

### 调试技巧

1. 启用详细日志查看订单处理过程
2. 使用统计信息监控队列状态
3. 运行演示程序验证功能正常
