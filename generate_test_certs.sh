#!/bin/bash

# 生成用于开发和测试的自签名证书
# 注意：这些证书仅用于开发测试，不应在生产环境中使用

set -e

CERT_DIR="./certs"
CERT_FILE="$CERT_DIR/server.crt"
KEY_FILE="$CERT_DIR/server.key"
DAYS=365
SUBJECT="/C=US/ST=Test/L=Test/O=Backtest/OU=Development/CN=localhost"

echo "Generating self-signed certificates for development..."

# 创建证书目录
mkdir -p "$CERT_DIR"

# 生成私钥
echo "Generating private key..."
openssl genrsa -out "$KEY_FILE" 2048

# 生成自签名证书
echo "Generating self-signed certificate..."
openssl req -new -x509 -key "$KEY_FILE" -out "$CERT_FILE" -days "$DAYS" -subj "$SUBJECT"

# 设置适当的权限
chmod 600 "$KEY_FILE"
chmod 644 "$CERT_FILE"

echo "Certificates generated successfully:"
echo "  Certificate: $CERT_FILE"
echo "  Private Key: $KEY_FILE"
echo ""
echo "To use these certificates, update your config.toml:"
echo ""
echo "[http_tls]"
echo "enabled = true"
echo ""
echo "[http_tls.cert_source]"
echo "type = \"Files\""
echo "cert_path = \"$CERT_FILE\""
echo "key_path = \"$KEY_FILE\""
echo ""
echo "[websocket_tls]"
echo "enabled = true"
echo ""
echo "[websocket_tls.cert_source]"
echo "type = \"Files\""
echo "cert_path = \"$CERT_FILE\""
echo "key_path = \"$KEY_FILE\""
echo ""
echo "WARNING: These are self-signed certificates for development only!"
echo "Your browser will show security warnings when accessing HTTPS endpoints."
