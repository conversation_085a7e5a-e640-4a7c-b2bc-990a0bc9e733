# 2025-01-14: Binance WebSocket API Tardis Integration

## 目标
实现通过backtest订阅Tardis的quotes和trades数据，提供与Binance期货相同的WebSocket接口，支持：
- `symbol@bookTicker` - 个人符号最佳买卖价格流
- `symbol@aggTrade` - 聚合交易流

## 实现内容

### 1. 扩展WebSocket消息结构
- 在 `src/websocket/handler.rs` 中添加了 `BinanceAggTrade` 结构体
- 支持Binance期货聚合交易流格式，包含所有必要字段：
  - `e`: 事件类型 ("aggTrade")
  - `E`: 事件时间
  - `s`: 交易对符号
  - `a`: 聚合交易ID
  - `p`: 价格
  - `q`: 数量
  - `f`: 第一个交易ID
  - `l`: 最后一个交易ID
  - `T`: 交易时间
  - `m`: 是否为买方maker

### 2. 更新订阅类型
- 在 `src/types.rs` 中扩展 `SubscriptionType` 枚举
- 添加了 `AggTrade` 订阅类型
- 保留了现有的 `BookTicker` 类型（对应quotes数据）

### 3. 增强数据转换功能
- 更新 `src/websocket/distributor.rs` 中的 `convert_to_binance_format` 方法
- 支持将Tardis TradeData转换为Binance AggTrade格式
- 支持将Tardis Bbo数据转换为Binance BookTicker格式
- 添加了动态流名称生成功能

### 4. 流名称解析支持
- 在 `src/websocket/handler.rs` 中更新 `parse_stream_name` 方法
- 支持解析 `symbol@aggTrade` 格式的流名称
- 保持对现有 `symbol@bookTicker` 格式的支持

### 5. WebSocket客户端工具增强
- 在 `tools/websocket_client.rs` 中添加了 `format_binance_agg_trade` 函数
- 支持格式化显示aggTrade消息，包含交易方向、价格、数量和时间信息

## 测试结果

### BookTicker流测试（Tardis Quotes数据）
```bash
cargo run --bin websocket_client -- -m binance -s "btcusdt@bookTicker" -d 10 --host 127.0.0.1 --port 8082
```
- ✅ 成功订阅
- ✅ 收到4669条BookTicker消息
- ✅ 平均每秒466.9条消息
- ✅ 数据格式完全符合Binance API标准

### AggTrade流测试（Tardis Trades数据）
```bash
cargo run --bin websocket_client -- -m binance -s "btcusdt@aggTrade" -d 10 --host 127.0.0.1 --port 8082
```
- ✅ 成功订阅
- ✅ 收到4699条AggTrade消息
- ✅ 平均每秒469.9条消息
- ✅ 数据格式完全符合Binance API标准
- ✅ 正确显示买卖方向（m字段）

## 数据格式示例

### BookTicker消息格式
```json
{
  "stream": "btcusdt@bookTicker",
  "data": {
    "e": "bookTicker",
    "u": 0,
    "E": 1752474066771,
    "T": 1752474066771,
    "s": "BTCUSDT",
    "b": "102437.80000000",
    "B": "4.24900000",
    "a": "102437.90000000",
    "A": "3.31900000"
  }
}
```

### AggTrade消息格式
```json
{
  "stream": "btcusdt@aggTrade",
  "data": {
    "e": "aggTrade",
    "E": 1746061470172,
    "s": "BTCUSDT",
    "a": 6262736580,
    "p": "94444.10000000",
    "q": "0.00400000",
    "f": 6262736580,
    "l": 6262736580,
    "T": 1746061470172,
    "m": false
  }
}
```

## 技术细节

### 数据流映射
- **Tardis Quotes** → **Binance BookTicker**: BBO数据转换为最佳买卖价格流
- **Tardis Trades** → **Binance AggTrade**: 交易数据转换为聚合交易流

### 订阅类型映射
- `MarketData::Bbo` → `SubscriptionType::BookTicker`
- `MarketData::TradeData` → `SubscriptionType::AggTrade`

### 流名称生成
- BookTicker: `{symbol}@bookTicker`
- AggTrade: `{symbol}@aggTrade`

## 配置要求

### 测试配置文件示例
```toml
exchange = "Binance"
start_time = "2025-05-01T00:00:00Z"
end_time = "2025-05-01T01:00:00Z"
websocket_port = 8082
data_source_type = "Tardis"
tardis_data_type = "Trades"  # 或 "Quotes"
```

## 成功标准
- ✅ 完全兼容Binance期货WebSocket API格式
- ✅ 支持实时数据流订阅和取消订阅
- ✅ 高性能数据推送（>400消息/秒）
- ✅ 正确的数据类型转换和格式化
- ✅ 稳定的WebSocket连接和消息处理

## 下一步
- 可以考虑添加更多Binance WebSocket流类型支持
- 优化数据转换性能
- 添加更多交易对支持
- 考虑添加深度数据流支持
