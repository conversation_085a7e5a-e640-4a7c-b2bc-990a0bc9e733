# pip install tardis-dev
# requires Python >=3.6
from tardis_dev import datasets, get_exchange_details
import logging
from datetime import datetime

# comment out to disable debug logs
logging.basicConfig(level=logging.DEBUG)


# function used by default if not provided via options
def default_file_name(exchange, data_type, date, symbol, format):
    return f"{exchange}_{data_type}_{date.strftime('%Y-%m-%d')}_{symbol}.{format}.gz"


# customized get filename function - saves data in nested directory structure
def file_name_nested(exchange, data_type, date, symbol, format):
    return f"{exchange}/{data_type}/{date.strftime('%Y-%m-%d')}_{symbol}.{format}.gz"


# returns data available at https://api.tardis.dev/v1/exchanges/deribit
details = get_exchange_details("binance-futures")
for sym in details["datasets"]["symbols"]:
    sym_id = sym["id"]
    if "TRUMP" in sym_id:
        print(sym["id"], sym["type"])

# exit(0)
datasets.download(
    # one of https://api.tardis.dev/v1/exchanges with supportsDatasets:true - use 'id' value
    exchange="binance-futures",
    # accepted data types - 'datasets.symbols[].dataTypes' field in https://api.tardis.dev/v1/exchanges/deribit,
    # or get those values from 'deribit_details["datasets"]["symbols][]["dataTypes"] dict above
    data_types=["trades", "quotes"],
    # change date ranges as needed to fetch full month or year for example
    from_date="2025-08-14",
    # to date is non inclusive
    to_date="2025-08-15",
    # accepted values: 'datasets.symbols[].id' field in https://api.tardis.dev/v1/exchanges/binance-futures
    symbols=["TRUMPUSDT"],
    # (optional) your API key to get access to non sample data as well
    api_key="TD.oixuUs3cr5-K01Yt.SLkjYwIC5N0Zd7P.kUKaN3ZjnopzbEk.9OQJ2z4BPuJEydX.wewoBTfcHw32FlH.08Wu",
    # (optional) path where data will be downloaded into, default dir is './datasets'
    # download_dir="./datasets",
    # (optional) - one can customize downloaded file name/path (flat dir strucure, or nested etc) - by default function 'default_file_name' is used
    # get_filename=default_file_name,
    # (optional) file_name_nested will download data to nested directory structure (split by exchange and data type)
    # get_filename=file_name_nested,
)
