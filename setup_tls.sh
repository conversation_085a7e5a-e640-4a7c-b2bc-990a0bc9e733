#!/bin/bash

# 一键TLS证书安装脚本
# 适用于新clone的仓库，让WebSocket客户端无需修改即可连接

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 显示标题
show_header() {
    echo -e "${BLUE}${BOLD}================================================${NC}"
    echo -e "${BLUE}${BOLD}  回测框架 TLS 证书一键安装脚本${NC}"
    echo -e "${BLUE}${BOLD}================================================${NC}"
    echo
    echo -e "${YELLOW}本脚本将：${NC}"
    echo -e "  1. 生成CA证书和服务器证书"
    echo -e "  2. 安装CA证书到系统证书存储"
    echo -e "  3. 配置TLS服务器"
    echo -e "  4. 验证安装结果"
    echo
    echo -e "${GREEN}安装完成后，你的WebSocket客户端无需修改代码即可连接！${NC}"
    echo
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}❌ 此脚本需要root权限来安装系统证书${NC}"
        echo -e "${YELLOW}请使用以下命令运行：${NC}"
        echo -e "${BLUE}sudo $0${NC}"
        exit 1
    fi
    echo -e "${GREEN}✓ 权限检查通过${NC}"
}

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"

    # 检查openssl
    if ! command -v openssl &> /dev/null; then
        echo -e "${RED}❌ 未找到openssl，请先安装${NC}"
        exit 1
    fi
    echo -e "${GREEN}✓ OpenSSL 已安装${NC}"

    # 检查curl
    if ! command -v curl &> /dev/null; then
        echo -e "${YELLOW}⚠ 未找到curl，将跳过HTTPS测试${NC}"
    else
        echo -e "${GREEN}✓ Curl 已安装${NC}"
    fi
    echo
}

# 生成证书
generate_certificates() {
    echo -e "${YELLOW}1. 生成证书...${NC}"

    if [[ ! -f "./generate_ca_certs.sh" ]]; then
        echo -e "${RED}❌ 未找到证书生成脚本${NC}"
        exit 1
    fi

    # 确保脚本可执行
    chmod +x ./generate_ca_certs.sh

    # 生成证书
    ./generate_ca_certs.sh

    # 确保证书文件权限正确（防止权限问题）
    echo -e "${BLUE}修复证书文件权限...${NC}"
    if [[ -n "$SUDO_USER" ]]; then
        # 如果是通过sudo运行，确保文件所有者是调用sudo的用户
        chown -R "$SUDO_USER:$SUDO_USER" ./certs/
        echo -e "${GREEN}✓ 证书文件所有者已设置为 $SUDO_USER${NC}"
    fi

    # 设置适当的权限
    chmod 600 ./certs/*.key 2>/dev/null || true
    chmod 644 ./certs/*.crt 2>/dev/null || true
    echo -e "${GREEN}✓ 证书文件权限已设置${NC}"

    echo -e "${GREEN}✓ 证书生成完成${NC}"
    echo
}

# 安装CA证书到系统
install_system_ca() {
    echo -e "${YELLOW}2. 安装CA证书到系统...${NC}"

    CA_CERT="./certs/ca.crt"

    if [[ ! -f "$CA_CERT" ]]; then
        echo -e "${RED}❌ CA证书文件不存在: $CA_CERT${NC}"
        exit 1
    fi

    # 检测操作系统并安装证书
    if [[ -d "/usr/local/share/ca-certificates" ]]; then
        # Ubuntu/Debian系统
        echo -e "${BLUE}检测到 Ubuntu/Debian 系统${NC}"
        cp "$CA_CERT" "/usr/local/share/ca-certificates/backtest-ca.crt"
        update-ca-certificates
        echo -e "${GREEN}✓ CA证书已安装到系统证书存储 (Ubuntu/Debian)${NC}"

    elif [[ -d "/etc/pki/ca-trust/source/anchors" ]]; then
        # CentOS/RHEL系统
        echo -e "${BLUE}检测到 CentOS/RHEL 系统${NC}"
        cp "$CA_CERT" "/etc/pki/ca-trust/source/anchors/backtest-ca.crt"
        update-ca-trust
        echo -e "${GREEN}✓ CA证书已安装到系统证书存储 (CentOS/RHEL)${NC}"

    elif [[ "$(uname)" == "Darwin" ]]; then
        # macOS系统
        echo -e "${BLUE}检测到 macOS 系统${NC}"
        security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain "$CA_CERT"
        echo -e "${GREEN}✓ CA证书已安装到系统钥匙串 (macOS)${NC}"

    else
        echo -e "${RED}❌ 未识别的操作系统，无法自动安装系统证书${NC}"
        echo -e "${YELLOW}请手动将 $CA_CERT 添加到系统证书存储${NC}"
        exit 1
    fi
    echo
}

# 验证证书安装
verify_installation() {
    echo -e "${YELLOW}3. 验证证书安装...${NC}"

    # 验证证书文件
    if openssl x509 -in "./certs/ca.crt" -text -noout > /dev/null 2>&1; then
        echo -e "${GREEN}✓ CA证书格式正确${NC}"
    else
        echo -e "${RED}❌ CA证书格式错误${NC}"
        exit 1
    fi

    if openssl verify -CAfile "./certs/ca.crt" "./certs/server.crt" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 证书链验证成功${NC}"
    else
        echo -e "${RED}❌ 证书链验证失败${NC}"
        exit 1
    fi

    echo -e "${GREEN}✓ 证书安装验证完成${NC}"
    echo
}

# 启动测试服务器
start_test_server() {
    echo -e "${YELLOW}4. 启动测试服务器...${NC}"

    # 检查配置文件
    if [[ ! -f "test_tls_config.toml" ]]; then
        echo -e "${RED}❌ 未找到TLS配置文件: test_tls_config.toml${NC}"
        exit 1
    fi

    # 编译项目
    echo -e "${BLUE}编译项目...${NC}"
    cargo build --bin backtest > /dev/null 2>&1

    # 启动服务器（后台运行）
    echo -e "${BLUE}启动TLS服务器...${NC}"
    cargo run --bin backtest -- --config test_tls_config.toml > server_test.log 2>&1 &
    SERVER_PID=$!

    # 等待服务器启动
    sleep 5

    # 检查服务器是否成功启动
    if kill -0 $SERVER_PID 2>/dev/null; then
        echo -e "${GREEN}✓ TLS服务器启动成功 (PID: $SERVER_PID)${NC}"
        echo "SERVER_PID=$SERVER_PID" > .test_server_pid
    else
        echo -e "${RED}❌ TLS服务器启动失败${NC}"
        cat server_test.log
        exit 1
    fi
    echo
}

# 测试连接
test_connections() {
    echo -e "${YELLOW}5. 测试连接...${NC}"

    # 测试HTTPS连接（无需指定CA证书）
    if command -v curl &> /dev/null; then
        echo -e "${BLUE}测试HTTPS连接...${NC}"
        if curl -s https://localhost:8083/api/v1/health > /dev/null 2>&1; then
            echo -e "${GREEN}✓ HTTPS连接成功（系统CA证书生效）${NC}"
        else
            echo -e "${YELLOW}⚠ HTTPS连接失败（可能端口冲突）${NC}"
        fi
    fi

    # 测试WSS连接
    echo -e "${BLUE}测试WSS连接...${NC}"
    if RUST_LOG=error cargo run --bin test_ca_client > /dev/null 2>&1; then
        echo -e "${GREEN}✓ WSS连接成功${NC}"
    else
        echo -e "${YELLOW}⚠ WSS连接测试失败${NC}"
    fi
    echo
}

# 清理测试服务器
cleanup_test_server() {
    if [[ -f ".test_server_pid" ]]; then
        SERVER_PID=$(cat .test_server_pid | cut -d'=' -f2)
        if kill -0 $SERVER_PID 2>/dev/null; then
            kill $SERVER_PID
            echo -e "${GREEN}✓ 测试服务器已停止${NC}"
        fi
        rm -f .test_server_pid server_test.log
    fi
}

# 修复证书权限（独立函数）
fix_certificate_permissions() {
    echo -e "${YELLOW}修复证书文件权限...${NC}"

    if [[ ! -d "./certs" ]]; then
        echo -e "${RED}❌ 证书目录不存在: ./certs${NC}"
        echo -e "${YELLOW}请先运行证书生成：./generate_ca_certs.sh${NC}"
        exit 1
    fi

    # 获取当前用户（如果通过sudo运行，获取原始用户）
    if [[ -n "$SUDO_USER" ]]; then
        TARGET_USER="$SUDO_USER"
    else
        TARGET_USER="$(whoami)"
    fi

    echo -e "${BLUE}设置证书文件所有者为: $TARGET_USER${NC}"

    # 修改所有者
    if [[ $EUID -eq 0 ]]; then
        chown -R "$TARGET_USER:$TARGET_USER" ./certs/
    else
        echo -e "${YELLOW}⚠ 非root用户，跳过所有者修改${NC}"
    fi

    # 设置权限
    chmod 755 ./certs/ 2>/dev/null || true
    chmod 600 ./certs/*.key 2>/dev/null || true
    chmod 644 ./certs/*.crt 2>/dev/null || true

    echo -e "${GREEN}✓ 证书权限修复完成${NC}"
    echo -e "${GREEN}  - 私钥文件权限: 600 (仅所有者可读写)${NC}"
    echo -e "${GREEN}  - 证书文件权限: 644 (所有者可读写，其他用户可读)${NC}"
    echo -e "${GREEN}  - 文件所有者: $TARGET_USER${NC}"
    echo
}

# 显示完成信息
show_completion() {
    echo -e "${GREEN}${BOLD}================================================${NC}"
    echo -e "${GREEN}${BOLD}  🎉 TLS证书安装完成！${NC}"
    echo -e "${GREEN}${BOLD}================================================${NC}"
    echo
    echo -e "${YELLOW}现在你可以：${NC}"
    echo
    echo -e "${GREEN}1. 启动TLS服务器：${NC}"
    echo -e "   ${BLUE}cargo run --bin backtest -- --config test_tls_config.toml${NC}"
    echo
    echo -e "${GREEN}2. 使用任何WebSocket客户端连接（无需修改代码）：${NC}"
    echo -e "   ${BLUE}wss://localhost:8082${NC}  (WebSocket Secure)"
    echo -e "   ${BLUE}https://localhost:8083${NC} (HTTPS API)"
    echo
    echo -e "${GREEN}3. 测试连接：${NC}"
    echo -e "   ${BLUE}curl https://localhost:8083/api/v1/health${NC}"
    echo -e "   ${BLUE}cargo run --bin test_ca_client${NC}"
    echo
    echo -e "${GREEN}4. 如果遇到权限问题：${NC}"
    echo -e "   ${BLUE}sudo ./setup_tls.sh --fix-permissions${NC}"
    echo
    echo -e "${YELLOW}客户端示例代码：${NC}"
    echo -e "   ${BLUE}Python:${NC} ws = websocket.WebSocket(); ws.connect('wss://localhost:8082')"
    echo -e "   ${BLUE}Node.js:${NC} const ws = new WebSocket('wss://localhost:8082')"
    echo -e "   ${BLUE}Rust:${NC} connect_async('wss://localhost:8082').await"
    echo
    echo -e "${GREEN}✨ 你的WebSocket客户端现在可以直接连接，无需任何TLS配置！${NC}"
    echo
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}${BOLD}用法：${NC}"
    echo -e "  $0                    # 完整安装TLS证书"
    echo -e "  $0 --fix-permissions  # 仅修复证书文件权限"
    echo -e "  $0 --help            # 显示此帮助信息"
    echo
}

# 主函数
main() {
    # 处理命令行参数
    case "${1:-}" in
        --fix-permissions)
            echo -e "${BLUE}${BOLD}修复证书权限模式${NC}"
            echo
            fix_certificate_permissions
            echo -e "${GREEN}${BOLD}权限修复完成！${NC}"
            echo -e "${YELLOW}现在你可以启动backtest服务器了：${NC}"
            echo -e "  ${BLUE}cargo run --bin backtest -- --config example_config.toml${NC}"
            exit 0
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            # 默认完整安装
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac

    show_header

    # 设置清理陷阱
    trap cleanup_test_server EXIT

    check_permissions
    check_dependencies
    generate_certificates
    install_system_ca
    verify_installation
    # start_test_server
    # test_connections
    # cleanup_test_server
    show_completion
}

# 运行主函数
main "$@"
