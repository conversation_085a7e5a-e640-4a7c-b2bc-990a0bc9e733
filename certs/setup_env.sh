#!/bin/bash
# TLS证书环境变量设置

export SSL_CERT_FILE="/home/<USER>/codes/backtest/./certs/ca-bundle.crt"
export SSL_CERT_DIR="/home/<USER>/codes/backtest/./certs"
export REQUESTS_CA_BUNDLE="/home/<USER>/codes/backtest/./certs/ca-bundle.crt"
export CURL_CA_BUNDLE="/home/<USER>/codes/backtest/./certs/ca-bundle.crt"

echo "TLS证书环境变量已设置:"
echo "  SSL_CERT_FILE=$SSL_CERT_FILE"
echo "  SSL_CERT_DIR=$SSL_CERT_DIR"
echo "  REQUESTS_CA_BUNDLE=$REQUESTS_CA_BUNDLE"
echo "  CURL_CA_BUNDLE=$CURL_CA_BUNDLE"
