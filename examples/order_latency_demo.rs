use backtest::config::OrderLatencyConfig;
use backtest::matching::OrderLatencySimulator;
use backtest::types::{Order, OrderSide, OrderStatus, OrderType, Price};
use chrono::Utc;

/// 创建测试订单
fn create_test_order(id: &str, side: OrderSide, price: f64, quantity: f64) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side,
        price: Some(Price::new(price)),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
        reduce_only: false,
    }
}

fn main() {
    println!("🚀 订单延迟模拟器演示");
    println!("===================");

    // 1. 演示基本延迟功能
    println!("\n📊 1. 基本延迟功能演示");
    demo_basic_latency();

    // 2. 演示随机延迟功能
    println!("\n🎲 2. 随机延迟功能演示");
    demo_random_latency();

    // 3. 演示多订单时间排序
    println!("\n⏰ 3. 多订单时间排序演示");
    demo_multiple_orders();

    // 4. 演示配置更新
    println!("\n⚙️ 4. 配置更新演示");
    demo_config_update();

    println!("\n✅ 演示完成！");
}

fn demo_basic_latency() {
    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 5000, // 5ms延迟
        max_queue_size: 100,
        random_latency: false,
    };

    let mut simulator = OrderLatencySimulator::new(config);
    let current_timestamp = 1000000; // 1秒

    // 添加订单
    let order = create_test_order("order1", OrderSide::Buy, 50000.0, 1.0);
    println!(
        "  📝 添加订单: {} (当前时间戳: {})",
        order.id, current_timestamp
    );

    simulator.add_order(order, current_timestamp).unwrap();
    println!("  📊 队列大小: {}", simulator.queue_size());

    // 检查延迟前
    let ready_orders = simulator.get_ready_orders(current_timestamp + 3000);
    println!("  ⏳ 3ms后可执行订单数: {}", ready_orders.len());

    // 检查延迟后
    let ready_orders = simulator.get_ready_orders(current_timestamp + 5000);
    println!("  ✅ 5ms后可执行订单数: {}", ready_orders.len());
    if !ready_orders.is_empty() {
        println!("  🎯 执行订单: {}", ready_orders[0].id);
    }
}

fn demo_random_latency() {
    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms基础延迟
        max_queue_size: 100,
        random_latency: true, // 启用随机延迟 (2.4ms - 3.6ms)
    };

    let mut simulator = OrderLatencySimulator::new(config);
    let current_timestamp = 2000000;

    println!("  🎲 基础延迟: 3ms, 随机范围: 2.4ms - 3.6ms");

    // 添加多个订单观察随机延迟
    for i in 1..=5 {
        let order = create_test_order(&format!("order{}", i), OrderSide::Buy, 50000.0, 1.0);
        simulator.add_order(order, current_timestamp).unwrap();
    }

    println!("  📊 添加了5个订单到延迟队列");

    // 检查下一个执行时间戳
    if let Some(next_timestamp) = simulator.next_execution_timestamp() {
        let delay = next_timestamp - current_timestamp;
        println!(
            "  ⏰ 下一个订单执行延迟: {}微秒 ({:.1}ms)",
            delay,
            delay as f64 / 1000.0
        );
    }

    // 获取统计信息
    let stats = simulator.get_stats();
    println!("  📈 统计信息:");
    println!("    - 队列大小: {}", stats.queue_size);
    println!("    - 启用状态: {}", stats.enabled);
    println!("    - 配置延迟: {}微秒", stats.latency_micros);
    println!("    - 随机延迟: {}", stats.random_latency);
}

fn demo_multiple_orders() {
    let config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 2000, // 2ms延迟
        max_queue_size: 100,
        random_latency: false,
    };

    let mut simulator = OrderLatencySimulator::new(config);
    let base_timestamp = 3000000;

    println!("  📝 添加3个订单，时间间隔1ms");

    // 添加3个订单，时间间隔1ms
    for i in 1..=3 {
        let order = create_test_order(&format!("order{}", i), OrderSide::Buy, 50000.0, 1.0);
        let timestamp = base_timestamp + (i - 1) * 1000; // 每个订单间隔1ms
        simulator.add_order(order, timestamp).unwrap();
        println!("    订单{} 添加时间戳: {}", i, timestamp);
    }

    // 模拟时间推进，检查订单执行顺序
    println!("\n  ⏰ 时间推进，检查订单执行顺序:");

    for check_time in [
        base_timestamp + 2000,
        base_timestamp + 3000,
        base_timestamp + 4000,
        base_timestamp + 5000,
    ] {
        let ready_orders = simulator.get_ready_orders(check_time);
        if !ready_orders.is_empty() {
            for order in ready_orders {
                println!("    时间戳 {} 执行订单: {}", check_time, order.id);
            }
        }
    }
}

fn demo_config_update() {
    let initial_config = OrderLatencyConfig {
        enabled: false,
        latency_micros: 1000,
        max_queue_size: 100,
        random_latency: false,
    };

    let mut simulator = OrderLatencySimulator::new(initial_config);
    println!("  📊 初始配置: 禁用状态");

    // 尝试添加订单（应该失败）
    let order = create_test_order("order1", OrderSide::Buy, 50000.0, 1.0);
    let result = simulator.add_order(order, 4000000);
    println!("  ❌ 禁用状态下添加订单结果: {:?}", result.is_err());

    // 更新配置启用延迟模拟
    let new_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 4000, // 4ms延迟
        max_queue_size: 100,
        random_latency: true,
    };

    simulator.update_config(new_config);
    println!("  ⚙️ 更新配置: 启用延迟模拟, 4ms延迟, 随机延迟");

    // 现在添加订单应该成功
    let order = create_test_order("order2", OrderSide::Sell, 51000.0, 0.5);
    let result = simulator.add_order(order, 4000000);
    println!("  ✅ 启用状态下添加订单结果: {:?}", result.is_ok());
    println!("  📊 队列大小: {}", simulator.queue_size());

    // 显示最终统计
    let stats = simulator.get_stats();
    println!("  📈 最终统计:");
    println!("    - 队列大小: {}", stats.queue_size);
    println!("    - 延迟时间: {}微秒", stats.latency_micros);
    println!("    - 随机延迟: {}", stats.random_latency);
}
