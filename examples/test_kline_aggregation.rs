use backtest::types::{BacktestBbo, BacktestOrder, OrderSide, OrderStatus, OrderType, Price};
use chrono::{DateTime, Utc};
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 K线聚合功能测试");
    println!("==================");

    // 创建测试数据：同一秒内多个BBO数据
    let base_time = Utc::now();
    let mut bbo_records = Vec::new();
    let mut orders = Vec::new();

    // 第1秒：价格从50000到50010，应该聚合为 O:50000, H:50010, L:50000, C:50010
    for i in 0..11 {
        bbo_records.push(BacktestBbo {
            timestamp: base_time + chrono::Duration::milliseconds(i * 90), // 同一秒内
            bid_price: Price::new(50000.0 + i as f64),
            bid_quantity: 1.0,
            ask_price: Price::new(50001.0 + i as f64),
            ask_quantity: 1.0,
        });
    }

    // 第2秒：价格从50010到50005，应该聚合为 O:50010, H:50010, L:50005, C:50005
    for i in 0..6 {
        bbo_records.push(BacktestBbo {
            timestamp: base_time
                + chrono::Duration::seconds(1)
                + chrono::Duration::milliseconds(i * 150),
            bid_price: Price::new(50010.0 - i as f64),
            bid_quantity: 1.0,
            ask_price: Price::new(50011.0 - i as f64),
            ask_quantity: 1.0,
        });
    }

    // 第3秒：价格从50005到50020，应该聚合为 O:50005, H:50020, L:50005, C:50020
    for i in 0..16 {
        bbo_records.push(BacktestBbo {
            timestamp: base_time
                + chrono::Duration::seconds(2)
                + chrono::Duration::milliseconds(i * 60),
            bid_price: Price::new(50005.0 + i as f64),
            bid_quantity: 1.0,
            ask_price: Price::new(50006.0 + i as f64),
            ask_quantity: 1.0,
        });
    }

    // 添加一些订单
    orders.push(BacktestOrder {
        order_id: "buy1".to_string(),
        client_order_id: "client_buy1".to_string(),
        timestamp: base_time + chrono::Duration::milliseconds(500),
        symbol: "BTCUSDT".to_string(),
        side: OrderSide::Buy,
        order_type: OrderType::Market,
        quantity: 0.1,
        price: None,
        filled_price: Some(Price::new(50005.5)),
        filled_quantity: 0.1,
        status: OrderStatus::Filled,
        filled_timestamp: Some(base_time + chrono::Duration::milliseconds(500)),
        fee: 0.001,
        fee_asset: "USDT".to_string(),
    });

    orders.push(BacktestOrder {
        order_id: "sell1".to_string(),
        client_order_id: "client_sell1".to_string(),
        timestamp: base_time + chrono::Duration::seconds(1) + chrono::Duration::milliseconds(300),
        symbol: "BTCUSDT".to_string(),
        side: OrderSide::Sell,
        order_type: OrderType::Market,
        quantity: 0.1,
        price: None,
        filled_price: Some(Price::new(50008.5)),
        filled_quantity: 0.1,
        status: OrderStatus::Filled,
        filled_timestamp: Some(
            base_time + chrono::Duration::seconds(1) + chrono::Duration::milliseconds(300),
        ),
        fee: 0.001,
        fee_asset: "USDT".to_string(),
    });

    orders.push(BacktestOrder {
        order_id: "buy2".to_string(),
        client_order_id: "client_buy2".to_string(),
        timestamp: base_time + chrono::Duration::seconds(2) + chrono::Duration::milliseconds(800),
        symbol: "BTCUSDT".to_string(),
        side: OrderSide::Buy,
        order_type: OrderType::Market,
        quantity: 0.2,
        price: None,
        filled_price: Some(Price::new(50018.5)),
        filled_quantity: 0.2,
        status: OrderStatus::Filled,
        filled_timestamp: Some(
            base_time + chrono::Duration::seconds(2) + chrono::Duration::milliseconds(800),
        ),
        fee: 0.002,
        fee_asset: "USDT".to_string(),
    });

    println!("📊 测试数据统计:");
    println!("   - BBO记录数: {}", bbo_records.len());
    println!("   - 订单数: {}", orders.len());
    println!("   - 时间跨度: 3秒");

    // 生成HTML报告
    println!("\n🔧 生成K线图数据...");

    // 创建一个简单的HTML页面来测试K线图
    let html_content = format!(
        r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线聚合测试</title>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-container {{
            position: relative;
            height: 400px;
            margin: 20px 0;
        }}
        .info {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 K线聚合功能测试</h1>

        <div class="info">
            <h3>测试说明:</h3>
            <ul>
                <li><strong>第1秒</strong>: 价格从50000上升到50010 → 应显示绿色K线 (涨)</li>
                <li><strong>第2秒</strong>: 价格从50010下降到50005 → 应显示红色K线 (跌)</li>
                <li><strong>第3秒</strong>: 价格从50005上升到50020 → 应显示绿色K线 (涨)</li>
                <li><strong>买点</strong>: 绿色圆点，白色边框</li>
                <li><strong>卖点</strong>: 红色圆点，白色边框</li>
            </ul>
        </div>

        <div class="chart-container">
            <div id="klineChart"></div>
        </div>
    </div>

    <script>
        // K线图数据
        {}

        // 创建TradingView K线图
        const chartContainer = document.getElementById('klineChart');

        // 清空容器并设置样式
        chartContainer.innerHTML = '';
        chartContainer.style.height = '400px';
        chartContainer.style.width = '100%';

        // 创建图表
        const chart = LightweightCharts.createChart(chartContainer, {{
            width: chartContainer.clientWidth,
            height: 400,
            layout: {{
                background: {{
                    type: 'solid',
                    color: '#ffffff'
                }},
                textColor: '#333333'
            }},
            grid: {{
                vertLines: {{
                    color: '#e1e1e1'
                }},
                horzLines: {{
                    color: '#e1e1e1'
                }}
            }},
            crosshair: {{
                mode: LightweightCharts.CrosshairMode.Normal
            }},
            rightPriceScale: {{
                borderColor: '#cccccc'
            }},
            timeScale: {{
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: true
            }}
        }});

        // 添加K线系列
        const candlestickSeries = chart.addCandlestickSeries({{
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderDownColor: '#ef5350',
            borderUpColor: '#26a69a',
            wickDownColor: '#ef5350',
            wickUpColor: '#26a69a'
        }});

        // 设置K线数据
        candlestickSeries.setData(candlestickData);

        // 添加交易标记
        candlestickSeries.setMarkers(allMarkers);

        // 自适应图表大小
        chart.timeScale().fitContent();

        // 响应式调整
        window.addEventListener('resize', () => {{
            chart.applyOptions({{ width: chartContainer.clientWidth }});
        }});
    </script>
</body>
</html>
"#,
        // 这里我们需要手动调用 generate_kline_data 方法
        // 但由于它是私有方法，我们需要创建一个公共接口或者复制逻辑
        generate_test_kline_data(&bbo_records, &orders)
    );

    // 保存HTML文件
    let filename = format!(
        "kline_aggregation_test_{}.html",
        Utc::now().format("%Y%m%d_%H%M%S")
    );

    fs::write(&filename, html_content)?;

    println!("✅ 测试HTML文件已生成: {}", filename);
    println!("🌐 请在浏览器中打开该文件查看K线聚合效果");

    Ok(())
}

// 复制 generate_kline_data 的逻辑用于测试
fn generate_test_kline_data(bbo_records: &[BacktestBbo], orders: &[BacktestOrder]) -> String {
    use std::collections::BTreeMap;

    // 1秒K线数据结构
    #[derive(Debug, Clone)]
    struct KlineBar {
        timestamp: DateTime<Utc>,
        open: f64,
        high: f64,
        low: f64,
        close: f64,
    }

    // 将BBO数据聚合成1秒K线
    let mut kline_map: BTreeMap<i64, Vec<f64>> = BTreeMap::new();

    for bbo in bbo_records {
        let mid_price = (bbo.bid_price.value() + bbo.ask_price.value()) / 2.0;
        // 将时间戳截断到秒级别
        let second_timestamp = bbo.timestamp.timestamp();
        kline_map
            .entry(second_timestamp)
            .or_insert_with(Vec::new)
            .push(mid_price);
    }

    // 生成K线数据
    let mut kline_bars = Vec::new();
    for (timestamp, prices) in kline_map {
        if !prices.is_empty() {
            let open = prices[0];
            let close = prices[prices.len() - 1];
            let high = prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
            let low = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));

            kline_bars.push(KlineBar {
                timestamp: DateTime::from_timestamp(timestamp, 0).unwrap_or_default(),
                open,
                high,
                low,
                close,
            });
        }
    }

    // 生成Chart.js candlestick数据格式
    let mut candlestick_data = Vec::new();
    for bar in &kline_bars {
        candlestick_data.push(format!(
            "{{x: '{}', o: {:.4}, h: {:.4}, l: {:.4}, c: {:.4}}}",
            bar.timestamp.format("%H:%M:%S"),
            bar.open,
            bar.high,
            bar.low,
            bar.close
        ));
    }

    // 生成交易点位
    let mut buy_points = Vec::new();
    let mut sell_points = Vec::new();

    for order in orders {
        if let Some(price) = order.filled_price {
            let timestamp_str = order.timestamp.format("%H:%M:%S");
            let price_value = price.value();

            match order.side {
                OrderSide::Buy => {
                    buy_points.push(format!("{{x: '{}', y: {:.4}}}", timestamp_str, price_value));
                }
                OrderSide::Sell => {
                    sell_points.push(format!("{{x: '{}', y: {:.4}}}", timestamp_str, price_value));
                }
            }
        }
    }

    // 如果没有数据，提供默认值
    if candlestick_data.is_empty() {
        candlestick_data.push("{x: 'No Data', o: 0, h: 0, l: 0, c: 0}".to_string());
    }

    format!(
        r#"
        const klineData = {{
            candlestick: [{}],
            buyPoints: [{}],
            sellPoints: [{}]
        }};
        "#,
        candlestick_data.join(",\n            "),
        buy_points.join(",\n            "),
        sell_points.join(",\n            ")
    )
}
