use backtest::backtest_summary::HtmlGenerator;
use backtest::state::{get_backtest_recorder, set_backtest_recorder};
use backtest::types::{
    BacktestBbo, BacktestOrder, BacktestRecorder, BacktestTrade, OrderSide, OrderStatus, OrderType,
    Price,
};
use chrono::Utc;
use std::sync::Arc;
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 HTTP服务器回测总结功能测试");
    println!("================================");

    // 1. 初始化回测记录器
    println!("1. 初始化回测记录器...");
    let recorder = Arc::new(Mutex::new(BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;
    println!("   ✓ 回测记录器已初始化");

    // 2. 开始记录
    println!("2. 开始回测记录...");
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }
    println!("   ✓ 回测记录已开始");

    // 3. 模拟记录数据
    println!("3. 记录测试数据...");
    {
        let mut recorder = recorder.lock().await;

        // 记录BBO数据
        for i in 0..5 {
            let bbo = backtest::types::Bbo {
                update_id: i,
                bid_price: Price::new(50000.0 + i as f64),
                bid_quantity: 1.0,
                ask_price: Price::new(50001.0 + i as f64),
                ask_quantity: 1.0,
                timestamp: Some(
                    (Utc::now() + chrono::Duration::seconds(i as i64)).timestamp_micros() as u64,
                ),
                data_source_type: backtest::config::DataSourceType::BinanceOfficial,
            };
            recorder.record_bbo(&bbo);
        }

        // 记录订单
        for i in 0..3 {
            let order = backtest::types::Order {
                id: format!("order_{}", i),
                client_order_id: format!("client_{}", i),
                symbol: "BTCUSDT".to_string(),
                order_type: if i % 2 == 0 {
                    OrderType::Limit
                } else {
                    OrderType::Market
                },
                side: if i % 2 == 0 {
                    OrderSide::Buy
                } else {
                    OrderSide::Sell
                },
                price: Some(Price::new(50000.0 + i as f64)),
                quantity: 0.1,
                status: OrderStatus::Filled,
                timestamp: Utc::now() + chrono::Duration::seconds(i as i64),
                execution_info: None,
            };
            recorder.record_order(&order);
        }

        // 记录交易
        for i in 0..2 {
            let trade_record = backtest::account::types::TradeRecord::new(
                format!("trade_{}", i),
                format!("order_{}", i),
                "BTCUSDT".to_string(),
                if i % 2 == 0 {
                    OrderSide::Buy
                } else {
                    OrderSide::Sell
                },
                Price::new(50000.0 + i as f64),
                0.1,
                0.1,
                "USDT".to_string(),
                i % 2 == 0,
            );
            recorder.record_trade(&trade_record);
        }

        println!("   ✓ 记录了 {} 条BBO数据", recorder.bbo_records.len());
        println!("   ✓ 记录了 {} 条订单", recorder.orders.len());
        println!("   ✓ 记录了 {} 条交易", recorder.trades.len());
    }

    // 4. 停止记录
    println!("4. 停止回测记录...");
    {
        let mut recorder = recorder.lock().await;
        recorder.stop_recording(Utc::now());
    }
    println!("   ✓ 回测记录已停止");

    // 5. 生成总结
    println!("5. 生成回测总结...");
    let summary = {
        let recorder = recorder.lock().await;
        recorder.generate_summary()
    };

    match summary {
        Some(summary) => {
            println!("   ✓ 回测总结生成成功");
            println!("   📊 统计信息:");
            println!(
                "      - 回测期间: {} - {}",
                summary.start_time.format("%Y-%m-%d %H:%M:%S"),
                summary.end_time.format("%Y-%m-%d %H:%M:%S")
            );
            println!("      - 总盈亏: {:.2} USDT", summary.total_pnl);
            println!("      - 下单次数: {}", summary.total_orders);
            println!("      - 成交次数: {}", summary.total_fills);
            println!("      - 胜率: {:.1}%", summary.win_rate);
            println!("      - 年化收益率: {:.2}%", summary.annual_return);
            println!("      - 最大回撤: {:.2}%", summary.max_drawdown);

            // 6. 生成HTML报告
            println!("6. 生成HTML报告...");
            let html = HtmlGenerator::generate_summary_html(&summary);

            // 保存HTML文件用于对比
            let filename = format!("test_summary_{}.html", Utc::now().format("%Y%m%d_%H%M%S"));
            std::fs::write(&filename, &html)?;
            println!("   ✓ HTML报告已保存到: {}", filename);
        }
        None => {
            println!("   ❌ 生成回测总结失败");
            return Ok(());
        }
    }

    // 7. HTTP服务器信息
    println!("7. HTTP服务器访问信息:");
    println!("   🌐 服务器地址: http://localhost:8080");
    println!("   📋 可用端点:");
    println!("      - POST /api/v1/backtest/start  - 开始回测记录");
    println!("      - POST /api/v1/backtest/stop   - 停止回测记录");
    println!("      - GET  /api/v1/backtest/summary - 获取回测总结(JSON)");
    println!("      - GET  /backtest/summary        - 直接查看回测总结页面");
    println!("      - GET  /                        - 查看所有可用端点");

    println!("\n🎉 测试数据已准备完成！");
    println!("💡 现在可以启动HTTP服务器并访问回测总结页面了。");
    println!("💡 使用以下命令启动服务器:");
    println!("   cargo run --bin backtest-server");
    println!("💡 然后在浏览器中访问:");
    println!("   http://localhost:8080/backtest/summary");

    Ok(())
}
