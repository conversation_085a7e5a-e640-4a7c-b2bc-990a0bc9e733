# 🚀 快速开始指南

## TLS 准备

如果你刚刚clone了这个仓库，只需要运行一个命令：

```bash
sudo ./setup_tls.sh
```

## 使用

### 1. 启动backtest
```bash
./backtest --config test_tls_config.toml
```

### 2. 启动open_quant
```
configs/imbalance_strategy/config.toml为open_quant的配置
configs/imbalance_strategy/startegy.py 为策略文件，里面有目前支持的接口的使用方法
```

### 3. 开始回测
```
在backtest进程的cli里输入start
```

### 4. 回测可视化
```
https://<backtest_ip>:8085/backtest/summary
// 现在还需要手动刷新
```
