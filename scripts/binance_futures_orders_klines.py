#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Download Binance USDT-M Futures order history (allOrders) for a given symbol and time window,
fetch klines with the smallest available interval (try 1s, fallback to 1m),
align orders to nearest kline by order creation time, and output a single CSV file.

Usage:
  python scripts/binance_futures_orders_klines.py --symbol BTCUSDT --hours 12

Notes:
- Keys are hard-coded below per user's request. Replace placeholders with your actual keys.
- Align rule: nearest-neighbor by kline open time to the order creation time (order['time']).
- Output CSV is saved in current working directory.
"""

import csv
import datetime as dt
import hashlib
import hmac
import json
import math
import random
import sys
import time
from bisect import bisect_left
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlencode
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# ========= CONFIG =========
BASE_URL = "https://fapi.binance.com"
API_KEY = "loHWBMcaJimoA383ez9dIXfBMZy2eMcWJkHQtZNGLnGPX9QJOew7ihoinG4r6RpZ"
API_SECRET = "4rfju71r3TlX18hmPjRuviP8f2mzAegUQQwp69OjGRjj1qIxt7DaGTO1442WNHTl"
# Default recvWindow for signed requests
RECV_WINDOW = 5000
# Basic rate-limiting sleeps (seconds)
REQUEST_BASE_SLEEP = 0.2
MAX_RETRIES = 5

# ========= Helpers =========


def now_ms() -> int:
    return int(time.time() * 1000)


def sign_query(secret: str, query_string: str) -> str:
    return hmac.new(secret.encode("utf-8"), query_string.encode("utf-8"), hashlib.sha256).hexdigest()


def http_request(
    method: str,
    path: str,
    params: Dict[str, Any],
    *,
    signed: bool = False,
    api_key: Optional[str] = API_KEY,
    api_secret: Optional[str] = API_SECRET,
) -> Tuple[int, Dict[str, str], Any]:
    """Perform HTTP request to Binance Futures.

    Returns: (status_code, headers_dict, json_or_text)
    Raises: HTTPError, URLError for network-level issues
    """
    headers = {
        "User-Agent": "binance-futures-script/1.0",
    }

    query = dict(params) if params else {}
    if signed:
        query["timestamp"] = now_ms()
        query["recvWindow"] = RECV_WINDOW
        qs = urlencode(query, doseq=True)
        signature = sign_query(api_secret or "", qs)
        qs = f"{qs}&signature={signature}"
        headers["X-MBX-APIKEY"] = api_key or ""
    else:
        qs = urlencode(query, doseq=True)

    url = f"{BASE_URL}{path}"
    if method.upper() == "GET" and qs:
        url = f"{url}?{qs}"

    data_bytes = None
    if method.upper() in ("POST", "DELETE"):
        data_bytes = qs.encode("utf-8") if qs else b""
        headers["Content-Type"] = "application/x-www-form-urlencoded"

    req = Request(url=url, data=data_bytes, method=method.upper(), headers=headers)

    with urlopen(req, timeout=15) as resp:
        status = resp.getcode()
        resp_headers = {k: v for k, v in resp.getheaders()}
        raw = resp.read().decode("utf-8")
        try:
            body = json.loads(raw)
        except json.JSONDecodeError:
            body = raw
        return status, resp_headers, body


def request_with_retry(
    method: str,
    path: str,
    params: Dict[str, Any],
    *,
    signed: bool = False,
) -> Tuple[int, Dict[str, str], Any]:
    """Request with exponential backoff on 429/5xx and basic jitter.
    Returns: (status, headers, body)
    Raises: last HTTPError/URLError if all retries fail.
    """
    delay = REQUEST_BASE_SLEEP
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            status, headers, body = http_request(method, path, params, signed=signed)
            if status == 200:
                return status, headers, body
            # Handle error codes returned in JSON
            if isinstance(body, dict) and "code" in body:
                code = body.get("code")
                msg = body.get("msg")
                # Retry on rate limit or server error-like codes
                if status in (418, 429) or code in (-1003,):
                    # Rate limited – backoff
                    time.sleep(delay + random.uniform(0, delay))
                    delay = min(delay * 2, 5.0)
                    continue
                # Invalid interval etc. – return directly for caller to handle
                return status, headers, body
            # Non-200 without structured body – backoff a bit then possibly continue
            if status in (429, 418, 500, 503):
                time.sleep(delay + random.uniform(0, delay))
                delay = min(delay * 2, 5.0)
                continue
            return status, headers, body
        except HTTPError as e:
            # Convert HTTP errors to structured response when not retryable
            try:
                raw = e.read().decode("utf-8")
                parsed = json.loads(raw)
            except Exception:
                parsed = {"raw": getattr(e, "reason", str(e))}
            if e.code in (429, 418, 500, 503):
                time.sleep(delay + random.uniform(0, delay))
                delay = min(delay * 2, 5.0)
                continue
            # Return status/body for caller to handle (e.g., 400 invalid interval)
            try:
                headers_map = dict(e.headers.items()) if hasattr(e, "headers") and e.headers else {}
            except Exception:
                headers_map = {}
            return e.code, headers_map, parsed
        except URLError:
            time.sleep(delay + random.uniform(0, delay))
            delay = min(delay * 2, 5.0)
            continue
    # If we reach here, retries exhausted
    raise RuntimeError("Request failed after retries")


# ========= Core fetchers =========


def fetch_all_orders(symbol: str, start_ms: int, end_ms: int) -> List[Dict[str, Any]]:
    """Fetch all orders via /fapi/v1/allOrders within [start_ms, end_ms].
    Pagination uses startTime and last order's time.
    """
    path = "/fapi/v1/allOrders"
    orders: List[Dict[str, Any]] = []
    cursor = start_ms
    limit = 1000  # Binance typically allows up to 1000 for some endpoints; adjust on error

    while True:
        params = {
            "symbol": symbol,
            "startTime": cursor,
            "endTime": end_ms,
            "limit": limit,
        }
        status, headers, body = request_with_retry("GET", path, params, signed=True)
        if status != 200:
            # If limit too high, try smaller
            if isinstance(body, dict) and body.get("code") in (-1102, -1100):
                if limit > 500:
                    limit = 500
                    continue
            raise RuntimeError(f"Failed to fetch orders: status={status}, body={body}")

        batch = body if isinstance(body, list) else []
        if not batch:
            break

        # Filter to window and append
        for o in batch:
            t = int(o.get("time", 0))
            if start_ms <= t <= end_ms:
                orders.append(o)

        # Advance cursor
        last_time = int(batch[-1].get("time", cursor))
        if last_time <= cursor:
            # Avoid infinite loop
            break
        cursor = last_time + 1

        # Gentle pacing
        time.sleep(REQUEST_BASE_SLEEP)

        # Stop if we already passed
        if cursor > end_ms:
            break

    return orders


def fetch_klines(symbol: str, interval: str, start_ms: int, end_ms: int) -> List[List[Any]]:
    """Fetch klines via /fapi/v1/klines. Returns list of kline arrays.
    Each kline is: [openTime, open, high, low, close, volume, closeTime, ...]
    """
    path = "/fapi/v1/klines"
    klines: List[List[Any]] = []
    cursor = start_ms
    limit = 1500  # max for klines

    while True:
        params = {
            "symbol": symbol,
            "interval": interval,
            "startTime": cursor,
            "endTime": end_ms,
            "limit": limit,
        }
        status, headers, body = request_with_retry("GET", path, params, signed=False)
        if status != 200:
            raise RuntimeError(f"Failed to fetch klines: status={status}, body={body}")

        batch = body if isinstance(body, list) else []
        if not batch:
            break

        klines.extend(batch)
        last_open = int(batch[-1][0])
        next_cursor = last_open + 1
        if next_cursor <= cursor:
            break
        cursor = next_cursor

        time.sleep(REQUEST_BASE_SLEEP)
        if cursor > end_ms:
            break

    return klines


def fetch_user_trades(symbol: str, start_ms: int, end_ms: int) -> List[Dict[str, Any]]:
    """Fetch user trades via /fapi/v1/userTrades within [start_ms, end_ms].
    Pages by time using startTime cursor.
    """
    path = "/fapi/v1/userTrades"
    trades: List[Dict[str, Any]] = []
    cursor = start_ms
    limit = 1000

    while True:
        params = {
            "symbol": symbol,
            "startTime": cursor,
            "endTime": end_ms,
            "limit": limit,
        }
        status, headers, body = request_with_retry("GET", path, params, signed=True)
        if status != 200:
            raise RuntimeError(f"Failed to fetch user trades: status={status}, body={body}")

        batch = body if isinstance(body, list) else []
        if not batch:
            break

        trades.extend(batch)
        last_time = int(batch[-1].get("time", cursor))
        if last_time <= cursor:
            break
        cursor = last_time + 1

        time.sleep(REQUEST_BASE_SLEEP)
        if cursor > end_ms:
            break

    return trades


def enrich_orders_with_last_trade_fields(
    orders: List[Dict[str, Any]], trades: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """For each order, attach fields (commission, maker, realizedPnl) from the last trade of that order.
    If multiple trades map to an orderId, keep the one with the greatest time; if tie, keep the last encountered.
    """
    # Build last-trade map by orderId
    last_by_order: Dict[int, Dict[str, Any]] = {}
    for t in trades:
        try:
            oid = int(t.get("orderId")) if t.get("orderId") is not None else None
        except Exception:
            oid = None
        if oid is None:
            continue
        t_time = int(t.get("time", 0))
        prev = last_by_order.get(oid)
        if prev is None or t_time >= int(prev.get("time", 0)):
            last_by_order[oid] = t

    # Attach to orders
    enriched: List[Dict[str, Any]] = []
    for o in orders:
        oid = o.get("orderId")
        try:
            oid_int = int(oid) if oid is not None else None
        except Exception:
            oid_int = None
        lt = last_by_order.get(oid_int) if oid_int is not None else None
        # Copy and add fields
        o2 = dict(o)
        if lt is not None:
            o2["commission"] = lt.get("commission")
            o2["maker"] = lt.get("maker")
            o2["realizedPnl"] = lt.get("realizedPnl")
        else:
            o2["commission"] = None
            o2["maker"] = None
            o2["realizedPnl"] = None
        enriched.append(o2)
    return enriched


def probe_smallest_interval(symbol: str, start_ms: int, end_ms: int) -> str:
    """Try 1s first; if invalid interval, fallback to 1m."""
    # Minimal probe window
    probe_params = {
        "symbol": symbol,
        "interval": "1s",
        "startTime": max(start_ms, end_ms - 60_000),
        "endTime": end_ms,
        "limit": 1,
    }
    status, headers, body = request_with_retry("GET", "/fapi/v1/klines", probe_params, signed=False)
    if status == 200 and isinstance(body, list):
        return "1s"
    # If body indicates invalid interval, fallback
    if isinstance(body, dict):
        msg = (body.get("msg") or "").lower()
        if "interval" in msg and ("invalid" in msg or "not" in msg):
            return "1m"
    # Conservative fallback
    return "1m"


# ========= Alignment =========


def build_open_times(klines: List[List[Any]]) -> List[int]:
    return [int(k[0]) for k in klines]


def nearest_index(sorted_opens: List[int], target_ms: int) -> Optional[int]:
    if not sorted_opens:
        return None
    pos = bisect_left(sorted_opens, target_ms)
    if pos == 0:
        return 0
    if pos == len(sorted_opens):
        return len(sorted_opens) - 1
    before = sorted_opens[pos - 1]
    after = sorted_opens[pos]
    if abs(after - target_ms) < abs(target_ms - before):
        return pos
    else:
        return pos - 1


# ========= CSV =========


def to_float_safe(x: Any) -> Optional[float]:
    try:
        if x is None or x == "":
            return None
        return float(x)
    except Exception:
        return None


def compute_avg_price(order: Dict[str, Any]) -> Optional[float]:
    # Prefer avgPrice if present and > 0
    ap = to_float_safe(order.get("avgPrice"))
    if ap and ap > 0:
        return ap
    exec_qty = to_float_safe(order.get("executedQty"))
    cum_quote = to_float_safe(order.get("cumQuote"))
    if exec_qty and exec_qty > 0 and cum_quote is not None:
        try:
            return float(cum_quote) / float(exec_qty)
        except Exception:
            return None
    return None


def write_csv(
    out_path: str,
    orders: List[Dict[str, Any]],
    klines: List[List[Any]],
    symbol: str,
    start_ms: int,
    end_ms: int,
) -> None:
    """Write one row per ORDER, aligned to nearest kline, with kline OHLC and last-trade fields.
    CSV columns follow the spec in scripts/readme.md.
    """
    fields = [
        "market_time",  # aligned kline openTime (ms)
        "order_create_time",  # order time (ms)
        "open",
        "close",
        "high",
        "low",
        "order side",
        "order status",
        "order price",
        "commsion",
        "maker",
        "realized pnl",
        "symbol",
        "window_start",
        "window_end",
    ]

    opens = build_open_times(klines)

    with open(out_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fields)
        writer.writeheader()
        win_start = dt.datetime.fromtimestamp(start_ms / 1000, tz=dt.timezone.utc).isoformat().replace("+00:00", "Z")
        win_end = dt.datetime.fromtimestamp(end_ms / 1000, tz=dt.timezone.utc).isoformat().replace("+00:00", "Z")

        for o in orders:
            order_time = int(o.get("time", 0))
            idx = nearest_index(opens, order_time)
            if idx is not None and 0 <= idx < len(klines):
                k = klines[idx]
                market_time = int(k[0])
                o_open, o_high, o_low, o_close = str(k[1]), str(k[2]), str(k[3]), str(k[4])
            else:
                market_time = None
                o_open = o_high = o_low = o_close = None

            row = {
                "market_time": market_time,
                "order_create_time": order_time,
                "open": o_open,
                "close": o_close,
                "high": o_high,
                "low": o_low,
                "order side": o.get("side"),
                "order status": o.get("status"),
                "order price": o.get("price"),
                "commsion": o.get("commission"),  # from enriched last trade
                "maker": o.get("maker"),
                "realized pnl": o.get("realizedPnl"),
                "symbol": symbol,
                "window_start": win_start,
                "window_end": win_end,
            }
            writer.writerow(row)


# ========= Main =========


def parse_args(argv: List[str]) -> Tuple[str, int]:
    import argparse

    p = argparse.ArgumentParser(description="Download Binance Futures orders and klines, align, and export CSV")
    p.add_argument("--symbol", required=True, help="Symbol, e.g., BTCUSDT")
    p.add_argument("--hours", type=int, required=True, help="Hours to look back from now (UTC)")
    args = p.parse_args(argv)
    return args.symbol.upper(), int(args.hours)


def main(argv: List[str]) -> None:
    symbol, hours = parse_args(argv)

    if not API_KEY or API_KEY.startswith("REPLACE_"):
        print("ERROR: Please set API_KEY and API_SECRET in the script before running.", file=sys.stderr)
        sys.exit(2)
    if not API_SECRET or API_SECRET.startswith("REPLACE_"):
        print("ERROR: Please set API_KEY and API_SECRET in the script before running.", file=sys.stderr)
        sys.exit(2)

    end_ms = now_ms()
    start_ms = end_ms - hours * 3600 * 1000

    print(
        f"Fetching orders for {symbol} from {dt.datetime.fromtimestamp(start_ms/1000, tz=dt.timezone.utc)} to {dt.datetime.fromtimestamp(end_ms/1000, tz=dt.timezone.utc)} (UTC)"
    )
    orders = fetch_all_orders(symbol, start_ms, end_ms)
    print(f"Fetched {len(orders)} orders")

    # Probe smallest interval
    interval = probe_smallest_interval(symbol, start_ms, end_ms)
    print(f"Using kline interval: {interval}")

    klines = fetch_klines(symbol, interval, start_ms, end_ms)
    print(f"Fetched {len(klines)} klines")

    # Fetch user trades and enrich orders
    user_trades = fetch_user_trades(symbol, start_ms, end_ms)
    print(f"Fetched {len(user_trades)} user trades")
    orders = enrich_orders_with_last_trade_fields(orders, user_trades)

    # Output file name
    start_str = dt.datetime.fromtimestamp(start_ms / 1000, tz=dt.timezone.utc).strftime("%Y%m%dT%H%M%SZ")
    end_str = dt.datetime.fromtimestamp(end_ms / 1000, tz=dt.timezone.utc).strftime("%Y%m%dT%H%M%SZ")
    out_name = f"{symbol}_orders_klines_{interval}_{start_str}_{end_str}.csv"

    write_csv(out_name, orders, klines, symbol, start_ms, end_ms)
    print(f"Saved CSV: {out_name}")


if __name__ == "__main__":
    main(sys.argv[1:])
